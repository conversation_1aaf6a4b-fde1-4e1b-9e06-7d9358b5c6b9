<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.wbw.yyhis.mapper.B032Mapper">

    <sql id="selectPatientCards">
        SELECT b.visit_sn                                      as visitSn,
               b.medical_record_no                             as medicalRecordNo,
               b.hospitalization_times                         as hospitalizationCount,
               b.name                                          as patientName,
               b.current_bed_name                              as bedNo,
               b.visit_doctor_name                             as attendingPhysician,
               GROUP_CONCAT(d.diag_name SEPARATOR '，')         as admissionDiagnosis,
               TIMESTAMPDIFF(YEAR, e.date_of_birth, CURDATE()) as patientAge,
               b.admission_datetime                            as admissionTime,
               f.discharge_datetime                            as dischargeTime,
               DATEDIFF(CURDATE(), b.admission_datetime)       as hospitalizationDays,
               gender                                          as gender,
               e.ethnicity                                     as ethnicity,
               b.extend_data1                                  as remark,
               b.current_dept_code                             as currentDeptCode,
               b.visit_doctor_no                               as visitDoctorNo
        FROM b03_2 b

                 LEFT JOIN
             b02_3 d ON b.visit_sn = d.visit_sn and d.diag_type = '入院诊断'

                 LEFT JOIN
             b02_1 e ON b.visit_sn = e.visit_sn
                 left join b07_1 f on b.visit_sn = f.visit_sn
    </sql>

    <select id="selectPatientCardsByDeptCode" resultType="cn.wbw.yyhis.model.vo.PatientCardVO">
      <include refid="selectPatientCards"/>
        WHERE
            b.current_dept_code = #{departmentCode}
        and b.source = #{source}
        group by  b.visit_sn

    </select>

    <select id="countPatientsByDepartment" resultType="cn.wbw.yyhis.model.dto.DepartmentPatientCountDTO">
        select current_dept_code as departmentCode, current_dept_name as departmentName, count(*) as patientCount
        from b03_2
        where source = #{source}
        group by current_dept_name, current_dept_code
    </select>

    <select id="getPatientCard" resultType="cn.wbw.yyhis.model.vo.PatientCardVO">
        <include refid="selectPatientCards"/>
        where b.visit_sn = #{visitSn}
        group by  b.visit_sn
    </select>
</mapper>