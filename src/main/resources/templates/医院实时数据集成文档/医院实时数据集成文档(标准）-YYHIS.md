|<p>![图标

中度可信度描述已自动生成](Aspose.Words.6cf8a4c4-9241-43da-a136-52677f391869.001.png)                                                                            OTD-0002                            </p><p></p><p>医院端产品实时数据集成</p><p></p><p>接</p><p>口</p><p>说</p><p>明</p><p></p><p>2024年01月</p>|
| :- |

**修订历史记录**

|日期|版本|说明|作者|
| - | - | - | - |
|2021-09-13|1\.0.0|初稿确定并归档到正式文档|王鹏|
|2023-11-17|2\.0.0|实时集成接口统一|黄文|
|2024-01-19|3\.0.0|1\.对接系统场景增加DRG场景 2.删除DRG上报数据单独接口|乔浩阳|


1. # **整体说明**
该文档主要描述了我方系统在医院内如何与第三方的实时数据进行集成，包括了具体对接的系统，对接的方式
1. # **对接场景**
   1. ## <a name="_对接系统场景"></a>**对接系统场景**
<table><tr><th><b>系统</b></th><th><b>患者</b></th><th><b>实时数据</b></th><th><b>Tab_code</b></th><th><b>适用产品</b></th><th><b>说明</b></th></tr>
<tr><td rowspan="4">HIS</td><td rowspan="4">住院患者</td><td>医嘱数据</td><td>[b10_1](file:///C:\Users\<USER>\Documents\WXWork\1688855904488671\Cache\File\2023-10\实时数据表清单\b10_1.xlsx)</td><td>迎春花、向日葵</td><td>必须</td></tr>
<tr><td>申请单数据</td><td>[b10_1](file:///C:\Users\<USER>\Documents\WXWork\1688855904488671\Cache\File\2023-10\实时数据表清单\b10_1.xlsx)</td><td>迎春花、向日葵</td><td>必须</td></tr>
<tr><td>就诊信息数据</td><td>[b02_1](file:///C:\Users\<USER>\Documents\WXWork\1688855904488671\Cache\File\2023-10\实时数据表清单\b02_1.xlsx)</td><td>迎春花、向日葵</td><td>必须</td></tr>
<tr><td>诊断数据</td><td>[b02_3](file:///C:\Users\<USER>\Documents\WXWork\1688855904488671\Cache\File\2023-10\实时数据表清单\b02_3.xlsx)</td><td>迎春花、向日葵</td><td>必须</td></tr>
<tr><td rowspan="4">EMR</td><td rowspan="4">住院患者</td><td>入院记录数据</td><td>[b05_1](file:///C:\Users\<USER>\Documents\WXWork\1688855904488671\Cache\File\2023-10\实时数据表清单\b05_1.xlsx) ，[b05_1_1](file:///C:\Users\<USER>\Documents\WXWork\1688855904488671\Cache\File\2023-10\实时数据表清单\b05_1_1.xlsx)</td><td>迎春花、向日葵</td><td>必须</td></tr>
<tr><td>病程记录数据</td><td>[b06_1](file:///C:\Users\<USER>\Documents\WXWork\1688855904488671\Cache\File\2023-10\实时数据表清单\b06_1.xlsx)</td><td>迎春花、向日葵</td><td>必须，若不接入此数据，无法实时提醒首次治疗临床TNM分期、化疗记录规范等指标</td></tr>
<tr><td>诊断数据</td><td>[b02_3](file:///C:\Users\<USER>\Documents\WXWork\1688855904488671\Cache\File\2023-10\实时数据表清单\b02_3.xlsx)</td><td>迎春花、向日葵</td><td>必须</td></tr>
<tr><td>24小时入出院记录数据</td><td>[b07_2](file:///C:\Users\<USER>\Documents\WXWork\1688855904488671\Cache\File\2023-10\实时数据表清单\b07_2.xlsx)</td><td>迎春花、向日葵</td><td>必须</td></tr>
</table>


1. ## <a name="_场景下的事件"></a>**场景下的事件**
2\.1的每个场景都应该包括以下事件内容

|事件|事件说明|接口类型|
| - | - | - |
|单行新增|在保存或者其它按钮点击的时候，在对应系统数据库新增一行记录|保存|
|单行物理删除|在保存或者其它按钮点击的时候，在对应系统数据库删除一行记录|删除|
|单行逻辑删除|在保存或者其它按钮点击的时候，在对应系统数据库修改一行记录|保存|
|单行修改|在保存或者其它按钮点击的时候，在对应系统数据库修改一行记录|保存|
|批量多行新增|在保存或者其它按钮点击的时候，在对应系统数据库新增多行记录|保存|
|批量多行物理删除|在保存或者其它按钮点击的时候，在对应系统数据库删除多行记录|删除|
|批量多行逻辑删除|在保存或者其它按钮点击的时候，在对应系统数据库修改多行记录|保存|
|批量多行修改|在保存或者其它按钮点击的时候，在对应系统数据库修改多行记录|保存|
|批量多行物理删除，多行修改、多行新增|在保存或者其它按钮点击的时候，在对应系统数据库修改多行记录、保存多行记录、删除多行记录|<p>删除多行记录：调删除</p><p>修改多行记录：调保存</p><p>保存多行记录：调保存</p>|
|批量多行逻辑删除，多行修改、多行新增|在保存或者其它按钮点击的时候，在对应系统数据库修改多行记录、保存多行记录|<p>修改多行记录：调保存</p><p>保存多行记录：调保存</p>|

接口类型在后面对接方案时会使用，对接方案里面只有两种：1、保存  2、删除
1. # **对接方案**
   1. ## **对接业务流程**
![未命名文件(134)](Aspose.Words.6cf8a4c4-9241-43da-a136-52677f391869.002.png)

场景：[参考《对接系统场景》](#_对接系统场景)

事件：[参考《场景下的事件》](#_场景下的事件)

- 该部分内容第三方存在两种调用：
- 数据还未入库，就调用我方接口，
- 数据入库成功，才调用我方接口。推荐使用该情况
- 该部分内容第三方存在返回机制：
- 我方系统返回结果后才结束，该方式是在医院进行卡点的时候才适用
- 不管我方系统是否返回结果。推荐使用该情况

1. ## **保存接口技术方案**
   1. ### <a name="_toc12381"></a><a name="_toc12269"></a>**Restful（http+json）**
<a name="_toc3887"></a>POST：${ server\_url:port }/ngdoctor/dss/rest/data/recv，其中server\_url、port由我方提供
1. ### <a name="_toc42"></a>**请求Headers**

|**参数**|**参数名称**|**类型**|**说明**|<p>**是否**</p><p>**必填**</p>|**样例**|
| :-: | :-: | :-: | :-: | :-: | :-: |
|appKey|应用授权码|String|需要在肿瘤大数据治理与信息平台注册应用后生成|是|<p>d0VIZURKSHhGWmtsUzRLS25kMC9oQT09</p><p></p>|
|<p>timestamp</p><p></p>|请求时间戳|Long|<p>客户端产生本次请求的 unix 时间戳（UTC），精确到毫秒</p><p>当 securityLevel 赋值为 high 时必填</p>|条件必填|<p>1644286723584</p><p></p>|
|<p>X-Auth-Param</p><p></p>|<p>请求数据签名信息</p><p></p>|String|<p>通过 appSecretKey 加密后，封装后的认证参数</p><p>当 securityLevel 赋值为 high 时必填</p>|条件必填|YY d0VIZURKSHhGWmtsUzRLS25kMC9oQT09:ZjUyNDY2ODFmYjQ1Mjg3NzgyOWVjMThmZTg0NTQwNzViYmY1YWE2Ng==|
|<p>securityLevel</p><p></p>|安全认证级别|String|安全验证级别，low 代表低级别安全要求，high代表高级别验证要求|是|high|

- <a name="_toc15328"></a><a name="_toc17020"></a><a name="_toc15207"></a><a name="_1.2.1.1.3请求body体参数"></a>接口安全调用，签名算法说明

为了对请求进行安全验证，您必须在请求中提供以下项目：

1\. appKey  : 壹永公司肿瘤规范化诊疗及费用合理化系统为您提供的应用授权码

2\. timestamp : 客户端产生本次请求的 unix 时间戳（UTC），精确到毫秒

3\. X-Auth-Param ： 请求数据签名信息，通过 appSecretKey 加密后，封装后的认证参数

下面是针对发送到肿瘤规范化诊疗及费用合理化系统的请求进行身份验证的常规步骤。此处假设您已拥有了必要的应用授权码appKey和秘密访问密钥appSecretKey

*Step1：添加header: timestamp  值为客户端产生本次请求的时间戳（UTC），精确到毫秒*

例： HttpGet.setHeader("timestamp", 1644286723584);

*Step2：生成HMAC输入消息 StringToSign,格式如下：*

StringToSign= RequestURI+ '#' +

` `timestamp  

具体示例

StringToSign= 

` `'/dss/rest/data/recv'+ '#' +1644286723584

RequestURI: 请求地址URL的path部分，即url的port和query之间的字符串，

例如：

` `http://192.168.1.1:8080/dss/rest/data/recv?typeCode=A00001

则RequestURI=/dss/rest/data/recv

timestamp  ： 客户端产生本次请求的时间戳（UTC），精确到毫秒 例：1644286723584

*Step3：通过分配给客户的密钥(appSecretKey)和Step2生成的StringToSign通过HMAC-SHA1算法计算出消息摘要HMAC*

HMAC=hmacSha1(appSecretKey,StringToSign)

*Step4: 将Step3生成的二进制数据消息摘要进行BASE64编码*

Sign=BASE64Encode(HMAC)

*Step5：请求Header中添加**X-Auth-Param***

**X-Auth-Param**的值='YY'+空格+appKey+':'+Sign;

最后Header中的内容格式示例：

Content-Type:application/json; charset=UTF-8

appKey:d0VIZURKSHhGWmtsUzRLS25kMC9oQT09

timestamp:1644286723584

X-Auth-Param:YY XxSawiGLH13v3lTo:/bwyRqBBmAP34u5nb4iVmayt2oM=

说明：

Step3中的hmacSha1 为HMAC-SHA1算法的方法，例：Java可以使用

org.apache.commons.codec.digest.HmacUtils.hmacSha1(appSecretKey,StringToSign)

或

Step4中的BASE64Encode BASE64编码方法，例：Java可以使用

java.util.Base64.getEncoder().encodeToString(hmac)
1. ### **请求Body体参数**
   1. #### <a name="_参数要求"></a>**参数要求**
|**参数**|**参数名称**||**类型**|**说明**|<p>**是否**</p><p>**必填**</p>|**样例**|
| :-: | :-: | :-: | :-: | :-: | :-: | :-: |
|patientId|患者id||String|患者id|是||
|visitSn|患者就诊id||String|患者就诊id|是||
|doctorId|医生id||String|医生id|是||
|deptId|科室id||String|科室id|是||
|admissionDatetime|入院时间||String|入院时间格式：yyyy-MM-dd HH:mm:ss|是||
|dischargeDatetime|出院时间||String|出院时间格式：yyyy-MM-dd HH:mm:ss|是||
|inHospitalStatus|在院状态||Integer|<p>取值：</p><p>` `1： 在院</p><p>2：出院</p>|是||
|visit\_type|就诊类型||String|就诊类型：住院、门诊|是||
|visit\_times|就诊次数||Integer|门诊次数|否||
|visit\_datetime|就诊时间||String|门诊就诊时间|否||
|hospitalization\_times|住院次数||Integer|住院次数|否||
|current\_dept\_code|就诊科室编码||String|当前科室代码|否||
|current\_dept\_name|就诊科室名称||String|当前科室名称|是||
|visit\_doctor\_no|就诊医生代码||String|就诊医生代码|否||
|visit\_doctor\_name|就诊医生姓名||String|就诊医生名称|否||
|qualityTarget|质控场景||String|<p>取值：</p><p>QT001 – QT015</p>|否|QT001|
|patientInfo|患者信息||Object|当次就诊的患者信息|否||
|-- name|<p>姓名</p><p>--表示缩进，从属关系</p>||String|姓名|是||
|--gender|性别||String|<p>取值范围：</p><p>男</p><p>女</p><p>未知</p>|是||
|--patient\_gender|性别编码||String|性别编码|||
|--newbron\_mark|是否新生儿||String|是否新生儿|||
|--birthDate|出生日期||String|格式： yyyy-MM-dd 或者 yyyyMMdd|是||
|--maritalStatus|<p>婚姻状况</p><p></p>||Integer|<p>取值范围：</p><p>10	未婚</p><p>20	已婚</p><p>21	初婚</p><p>22	再婚</p><p>23	复婚</p><p>30	丧偶</p><p>40	离婚</p><p>90	未说明的婚姻状况 </p>|否||
|--pregnancyStatus|妊娠状况  ||Integer|<p>取值范围</p><p>1 怀孕  </p><p>0 未怀孕 </p>|否||
|dataPacket|数据包||List<Object>|实时发生变化的数据包|是||
|extend1|扩展字段1||String|扩展字段1|否||
|extend2|扩展字段2||String|扩展字段2|否||
|extend3|扩展字段3||String|扩展字段3|否||
|extend4|扩展字段4||String|扩展字段4|否||
|extend5|扩展字段5||String|扩展字段5|否||
|extend6|扩展字段6||String|扩展字段6|否||
|extend7|扩展字段7||String|扩展字段7|否||
|extend8|扩展字段8||String|扩展字段8|否||
1. #### **参数说明**
- dataPacket
- 可以传多个数据包，涉及传多个数据的场景，[参考《对接系统场景》](#_对接系统场景)
- tableCode有值，data包的内容必须要传
- Data的要求参考具体《表清单要求》
- 其它参数
- 参考参数要求
  1. #### **参数示例**
示例：内容

{

`    `"patientId": "80271534",

`    `"visitSn": "130237",

`    `"doctorId": "3608310",

`    `"deptId": "7155333",

`    `"patientInfo": {

`        `"gender": "男",

`        `"birthDate": "2000-03-15",

`        `"maritalStatus": 20,

`        `"pregnancyStatus": 0,

`        `"name": "张三"

`    `},

`    `"dataPacket": [

`        `{

`            `"tableCode": "b02\_3",

`            `"data": [

`                `{

`                    `"patient\_id": "00931222",

`                    `"visit\_sn": "00931222|4623477|1住院",

`                    `"visit\_type": "门诊",

`                    `"visit\_card\_no": "153898",

`                    `"medical\_record\_no": "869881",

`                    `"outpatient\_no": "4623477",

`                    `"visit\_times": "1",

`                    `"inpatient\_no": "4623477",

`                    `"hospitalization\_times ": "1",

`                    `"name": "张三",

`                    `"diag\_id": "10431",

`                    `"diag\_serial\_number": "1",

`                    `"diag\_type": "出院诊断",

`                    `"diag\_status": "初步诊断",

`                    `"diag\_code": "C34.901",

`                    `"diag\_name": "右肺下叶小细胞癌",

`                    `"diag\_explanation": "右肺下叶小细胞癌，未见癌转移",

`                    `"diag\_datetime": "2018-01-12 11:32:11",

`                    `"confirmed\_diag\_mark": "是",

`                    `"maindiag\_mark": "否",

`                    `"diag\_doctor\_no": "1021",

`                    `"diag\_doctor\_name": "",

`                    `"diag\_source": "",

`                    `"extend\_data1": "",

`                    `"extend\_data2": "",

`                    `"RECORD\_STATUS": "1",

`                    `"RECORD\_DATETIME": "",

`                    `"RECORD\_UPDATE\_DATETIME": ""

`                `}

`            `]

`        `}

`    `],

`    `"extend1": "",

`    `"extend2": "",

`    `"extend3": "",

`    `"extend4": "",

`    `"extend5": "",

`    `"extend6": "",

`    `"extend7": "",

`    `"extend8": ""

}

示例: 工具

![图形用户界面, 文本, 应用程序

描述已自动生成](Aspose.Words.6cf8a4c4-9241-43da-a136-52677f391869.003.png)

![](Aspose.Words.6cf8a4c4-9241-43da-a136-52677f391869.004.png)

![](Aspose.Words.6cf8a4c4-9241-43da-a136-52677f391869.005.png)

示例: 代码-java

Unirest.setTimeouts(0, 0);

HttpResponse<String> response = Unirest.post("${ server\_url }/dss/rest/data/recv")

.header("appKey", "d0VIZURKSHhGWmtsUzRLS25kMC9oQT09")

.header("timestamp", "1644286723584")

.header("X-Auth-Param", "YY d0VIZURKSHhGWmtsUzRLS25kMC9oQT09:ZjUyNDY2ODFmYjQ1Mjg3NzgyOWVjMThmZTg0NTQwNzViYmY1YWE2Ng==")

.header("securityLevel", "high")

.header("Content-Type", "application/json")

.body("{\"patientId\":\"80271534\",\"visitSn\":\"130237\",\"doctorId\":\"3608310\",\"deptId\":\"7155333\",\"patientInfo\":{\"gender\":\"男\",\"birthDate\":\"2000-03-15\",\"maritalStatus\":20,\"pregnancyStatus\":0,\"name\":\"张三\"},\"dataPacket\":[{\"tableCode\":\"b02\_3\",\"data\":[{\"patient\_id\":\"00931222\",\"visit\_sn\":\"00931222|4623477|1住院\",\"visit\_type\":\"门诊\",\"visit\_card\_no\":\"153898\",\"medical\_record\_no\":\"869881\",\"outpatient\_no\":\"4623477\",\"visit\_times\":\"1\",\"inpatient\_no\":\"4623477\",\"hospitalization\_times\":\"1\",\"name\":\"张三\",\"diag\_id\":\"10431\",\"diag\_serial\_number\":\"1\",\"diag\_type\":\"出院诊断\",\"diag\_status\":\"初步诊断\",\"diag\_code\":\"C34.901\",\"diag\_name\":\"右肺下叶小细胞癌\",\"diag\_explanation\":\"右肺下叶小细胞癌，未见癌转移\",\"diag\_datetime\":\"2018-01-1211:32:11\",\"confirmed\_diag\_mark\":\"是\",\"maindiag\_mark\":\"否\",\"diag\_doctor\_no\":\"1021\",\"diag\_doctor\_name\":\"\",\"diag\_source\":\"\",\"extend\_data1\":\"\",\"extend\_data2\":\"\",\"RECORD\_STATUS\":\"1\",\"RECORD\_DATETIME\":\"\",\"RECORD\_UPDATE\_DATETIME\":\"\"}]}],\"extend1\":\"\",\"extend2\":\"\",\"extend3\":\"\",\"extend4\":\"\",\"extend5\":\"\",\"extend6\":\"\",\"extend7\":\"\",\"extend8\":\"\"}")

.asString();

示例: 代码-python

**import** requests

**import** json

url = "${ server\_url }/dss/rest/data/recv"

payload = json.dumps({"patientId":"80271534","visitSn":"130237","doctorId":"3608310","deptId":"7155333","patientInfo":{"gender":"男","birthDate":"2000-03-15","maritalStatus":20,"pregnancyStatus":0,"name":"张三"},"dataPacket":[{"tableCode":"b02\_3","data":[{"patient\_id":"00931222","visit\_sn":"00931222|4623477|1住院","visit\_type":"门诊","visit\_card\_no":"153898","medical\_record\_no":"869881","outpatient\_no":"4623477","visit\_times":"1","inpatient\_no":"4623477","hospitalization\_times":"1","name":"张三","diag\_id":"10431","diag\_serial\_number":"1","diag\_type":"出院诊断","diag\_status":"初步诊断","diag\_code":"C34.901","diag\_name":"右肺下叶小细胞癌","diag\_explanation":"右肺下叶小细胞癌，未见癌转移","diag\_datetime":"2018-01-1211:32:11","confirmed\_diag\_mark":"是","maindiag\_mark":"否","diag\_doctor\_no":"1021","diag\_doctor\_name":"","diag\_source":"","extend\_data1":"","extend\_data2":"","RECORD\_STATUS":"1","RECORD\_DATETIME":"","RECORD\_UPDATE\_DATETIME":""}]}],"extend1":"","extend2":"","extend3":"","extend4":"","extend5":"","extend6":"","extend7":"","extend8":""})

headers = {

`  `'appKey': 'd0VIZURKSHhGWmtsUzRLS25kMC9oQT09',

`  `'timestamp': '1644286723584',

`  `'X-Auth-Param': 'YY d0VIZURKSHhGWmtsUzRLS25kMC9oQT09:ZjUyNDY2ODFmYjQ1Mjg3NzgyOWVjMThmZTg0NTQwNzViYmY1YWE2Ng==',

`  `'securityLevel': 'high',

`  `'Content-Type': 'application/json'

}

response = requests.request("POST", url, headers=headers, data=payload)

**print**(response.text)
1. ### <a name="_toc12628"></a>**返回数据**
该部分内容，如果医院不做卡点，第三方可忽略。配合接口应用场景下的医院调用方式1配套使用

|**名称**|**类型**|**备注**||
| :-: | :-: | :-: | :-: |
|Success|boolean|true:成功 false:失败||
|Message|string|返回描述||
|Code|string|返回编码||
|Result|object|返回结果||

1. ### <a name="_toc19373"></a><a name="_toc8723"></a>**响应码描述**

|**响应码**|**描述**|
| :-: | :-: |
|200|请求成功|
|E000001|请求参数为空|
|E000002|请求参数格式错误|
|E000003|缺少必填参数|
|E000004|缺少请求token|
|E000005|token错误|
|E000006|未找到对应的API服务|
|E100000|患者就诊信息不存在|
|E100001|单病种质控方案不存在|
|E999999|系统异常|
|||
1. ### <a name="_场景清单"></a>**各场景表中相关字段说明**
详细字段说明请参见附表：实时数据表清单中各表的详细字段介绍。示例如图所示：

![](Aspose.Words.6cf8a4c4-9241-43da-a136-52677f391869.006.png)

在传入的参数中数据JSON串的KEY取自表清单中 ”目标字段（英文）”,VALUE为传入的数据。

**说明：表中以下字段为我方实时接口自动填充，不需要在参数中加入KEY**。

|**目标字段（英文）**|**描述**|
| :-: | :-: |
|hospital\_code|组织机构代码|
|hospital\_name|组织机构名称|
|yy\_collection\_datetime|数据采集时间|
|yy\_record\_md5|数据行唯一记录|
|yy\_record\_id|数据唯一标识|
|yy\_update\_datetime|数据更新时间|
|from\_table|来源表|
|from\_yy\_record\_id|来源id|
|patient\_id\_old|患者原始ID|
1. ## **删除接口技术方案**
在医院本身删除数据是物理删除的情况下，如果无法通过状态给我方传值。则可以单独调用下面内容，实现数据删除效果。
1. ### **根据联合主键删除（支持单条/批量）**
详细字段说明请参见附表：实时数据表清单中各表的详细字段介绍
1. ### **Restful（http+json）**
POST：${ server\_url : port }/ngdoctor /dss/rest/data/delete，其中server\_url、port由我方提供
1. ### **请求headers**

|**参数**|**参数名称**|**类型**|**说明**|<p>**是否**</p><p>**必填**</p>|**样例**|
| :-: | :-: | :-: | :-: | :-: | :-: |
|appKey|应用授权码|String|需要在肿瘤大数据治理与信息平台注册应用后生成|是|<p>d0VIZURKSHhGWmtsUzRLS25kMC9oQT09</p><p></p>|
|<p>timestamp</p><p></p>|请求时间戳|Long|<p>客户端产生本次请求的 unix 时间戳（UTC），精确到毫秒</p><p>当 securityLevel 赋值为 high 时必填</p>|条件必填|<p>1644286723584</p><p></p>|
|<p>X-Auth-Param</p><p></p>|<p>请求数据签名信息</p><p></p>|String|<p>通过 appSecretKey 加密后，封装后的认证参数</p><p>当 securityLevel 赋值为 high 时必填</p>|条件必填|YY d0VIZURKSHhGWmtsUzRLS25kMC9oQT09:ZjUyNDY2ODFmYjQ1Mjg3NzgyOWVjMThmZTg0NTQwNzViYmY1YWE2Ng==|
|<p>securityLevel</p><p></p>|安全认证级别|String|安全验证级别，low 代表低级别安全要求，high代表高级别验证要求|是|high|

1. ### **请求Body参数示例**

|<p>{</p><p>`    `"patientId": "80271534",</p><p>`    `"visitSn": "130237",</p><p>`    `"doctorId": "3608310",</p><p>`    `"deptId": "7155333",</p><p>`    `"dataPacket": [</p><p>`        `{</p><p>`            `"tableCode": "b02\_3",</p><p>`            `"data": [</p><p>`                `{ "visit\_sn": "00931222|4623477|1住院",</p><p>`                      `"diag\_type": "出院诊断",</p><p>`                      `"diag\_status": "初步诊断",</p><p>`                      `"diag\_code": "C34.901",</p><p>`                      `"diag\_name": "右肺下叶小细胞癌",</p><p>`                      `"diag\_source": "HIS"}</p><p>`         `]</p><p>`        `}</p><p>`    `],</p><p>`    `"extend1": "",</p><p>`    `"extend2": "",</p><p>`    `"extend3": "",</p><p>`    `"extend4": "",</p><p>`    `"extend5": "",</p><p>`    `"extend6": "",</p><p>`    `"extend7": "",</p><p>`    `"extend8": ""</p><p>}</p><p></p>|
| :- |
- 参数说明
- tableCode 	删除数据所在的表
- Date传入字段为当前表根据业务定义的联合主键；也可以按照医院删除的字段传
- deptId 当前医生登录的科室id，不能为空,和离线采集的科室id一致
- doctorId 为当前医生的id，不能为空
- patientId(必填):患者的id，需要保证与离线数据的患者id一致（一定要是患者的唯一标识）,
- visits(必填):就诊id，需要保证与离线数据的就诊id一致（一定要是患者就诊的唯一标识）,
- 各表的联合主键参见附表：实时数据表清单中各表的详细字段介绍中“是否联合主键”列为 “是” 的。
  1. ## **响应码描述**

|**响应码**|**描述**|
| :-: | :-: |
|200|请求成功|
|E000001|请求参数为空|
|E000002|请求参数格式错误|
|E000003|缺少必填参数|
|E000004|缺少请求token|
|E000005|token错误|
|E000006|未找到对应的API服务|
|E100000|患者就诊信息不存在|
|E100001|单病种质控方案不存在|
|E999999|系统异常|



1. # ` 	`**保存场景清单示例**
具体场景清单请参考《对接系统场景》，此处仅做相关枚举。
1. ## <a name="_toc17838"></a>**住院医嘱**
- 传入必填参数

|<p>{</p><p>`    `"patientId": "80271534",</p><p>`    `"visitSn": "130237",</p><p>`    `"doctorId": "3608310",</p><p>`    `"deptId": "7155333",</p><p>`    `"patientInfo": {</p><p>`        `"gender": "男",</p><p>`        `"birthDate": "2000-03-15",</p><p>`        `"maritalStatus": 20,</p><p>`        `"pregnancyStatus": 0,</p><p>`        `"name": "张三"</p><p>`    `},</p><p>`    `"dataPacket": [</p><p>`        `{</p><p>`            `"tableCode": "b10\_1",</p><p>`            `"data": [</p><p>`                `{</p><p>`                    `"patient\_id": "00931222",</p><p>`                    `"visit\_sn": "00931222|4623477|1住院",</p><p>`                        `"record\_status": 1,</p><p>                    .... 剩余字段请参考 本文档《实时表清单》各场景相关字段说明 </p><p>`                `}</p><p>`            `]</p><p>`        `}</p><p>`    `],</p><p>`    `"extend1": "",</p><p>`    `"extend2": "",</p><p>`    `"extend3": "",</p><p>`    `"extend4": "",</p><p>`    `"extend5": "",</p><p>`    `"extend6": "",</p><p>`    `"extend7": "",</p><p>`    `"extend8": ""</p><p>}</p><p></p>|
| :- |
- 参数说明
  - patient\_id		患者在本院唯一标识
  - visit\_sn		患者本次就诊唯一标识
  - record\_status	数据有效状态  1-有效数据  0-无效数据 （删除数据时,该字段值固定为0,其余为1）
  - deptId 当前医生登录的科室id，不能为空,和离线采集的科室id一致，(同管理后台-医院配置-白名单配置中科室id一致)
  - doctorId 为当前医生的id，不能为空，（同管理后台-医院配置-白名单配置中上传的医生id保持一致）
  - patientInfo  可以不传(如果传了则patientInfo对象属性里maritalStatus 婚姻状况/ pregnancyStatus 妊娠状况字段必须为Integer类型，name姓名、gender性别为字符类型，birthDate格式为yyyy-MM-dd)
  - 其余参数，根据b10\_1表编码，参考附录内容
  1. ## <a name="_toc16154"></a>**住院申请单**
- 传入必填参数

|<p>{</p><p>`    `"patientId": "80271534",</p><p>`    `"visitSn": "130237",</p><p>`    `"doctorId": "3608310",</p><p>`    `"deptId": "7155333",</p><p>`    `"patientInfo": {</p><p>`        `"gender": "男",</p><p>`        `"birthDate": "2000-03-15",</p><p>`        `"maritalStatus": 20,</p><p>`        `"pregnancyStatus": 0,</p><p>`        `"name": "张三"</p><p>`    `},</p><p>`    `"dataPacket": [</p><p>`        `{</p><p>`            `"tableCode": "b10\_1",</p><p>`            `"data": [</p><p>`                `{</p><p>`                    `"patient\_id": "00931222",</p><p>`                    `"visit\_sn": "00931222|4623477|1住院",</p><p>`                        `"record\_status": 1,</p><p>                    .... 剩余字段请参考 本文档《实时表清单》各场景相关字段说明 </p><p>`                `}</p><p>`            `]</p><p>`        `}</p><p>`    `],</p><p>`    `"extend1": "",</p><p>`    `"extend2": "",</p><p>`    `"extend3": "",</p><p>`    `"extend4": "",</p><p>`    `"extend5": "",</p><p>`    `"extend6": "",</p><p>`    `"extend7": "",</p><p>`    `"extend8": ""</p><p>}</p><p></p>|
| :- |
- 参数说明
  1. patient\_id		患者在本院唯一标识
  1. visit\_sn		患者本次就诊唯一标识
  1. record\_status	数据有效状态  1-有效数据  0-无效数据 （删除数据时,该字段值固定为0,其余为1）
  1. deptId 当前医生登录的科室id，不能为空,和离线采集的科室id一致，(同管理后台-医院配置-白名单配置中科室id一致)
  1. doctorId 为当前医生的id，不能为空，（同管理后台-医院配置-白名单配置中上传的医生id保持一致）
  1. patientInfo  可以不传，如果传了则patientInfo对象属性里maritalStatus 婚姻状况/ pregnancyStatus 妊娠状况字段必须为Integer类型，其余字段为String类型
- 其余参数，根据b10\_1表编码，参考附录内容

1. ## <a name="_toc13278"></a>**入院记录**
- 传入必填参数

|<p>{</p><p>`    `"patientId": "80271534",</p><p>`    `"visitSn": "130237",</p><p>`    `"doctorId": "3608310",</p><p>`    `"deptId": "7155333",</p><p>`    `"patientInfo": {</p><p>`        `"gender": "男",</p><p>`        `"birthDate": "2000-03-15",</p><p>`        `"maritalStatus": 20,</p><p>`        `"pregnancyStatus": 0,</p><p>`        `"name": "张三"</p><p>`    `},</p><p>`    `"dataPacket": [</p><p>`        `{</p><p>`            `"tableCode": "b05\_1",</p><p>`            `"data": [</p><p>`                `{</p><p>`                    `"patient\_id": "00931222",</p><p>`                    `"visit\_sn": "00931222|4623477|1住院",</p><p>`                    `"record\_status": 1,</p><p>                  .... 剩余字段请参考 本文档《实时表清单》各场景相关字段说明 </p><p>`                `}</p><p>`            `]</p><p>`        `},</p><p>`        `{</p><p>`            `"tableCode": "b05\_1\_1",</p><p>`            `"data": [</p><p>`                `{</p><p>`                    `"patient\_id": "00931222",</p><p>`                    `"visit\_sn": "00931222|4623477|1住院",</p><p>`                    `"record\_status": 1,</p><p>                  .... 剩余字段请参考 本文档《实时表清单》各场景相关字段说明</p><p>`                `}</p><p>`            `]</p><p>`        `}</p><p>`    `],</p><p>`    `"extend1": "",</p><p>`    `"extend2": "",</p><p>`    `"extend3": "",</p><p>`    `"extend4": "",</p><p>`    `"extend5": "",</p><p>`    `"extend6": "",</p><p>`    `"extend7": "",</p><p>`    `"extend8": ""</p><p>}</p><p></p>|
| :- |
- 参数说明
  1. patient\_id		患者在本院唯一标识
  1. visit\_sn		患者本次就诊唯一标识
  1. record\_status	数据有效状态  1-有效数据  0-无效数据 （删除数据时,该字段值固定为0,其余为1）
  1. 如果b05\_1\_1表无数据，可不传
- 其余参数，根据b05\_1 / b05\_1\_1 表编码，参考附录内容

1. ## <a name="_toc21100"></a>**病程记录**
- 传入必填参数

|<p>{</p><p>`    `"patientId": "80271534",</p><p>`    `"visitSn": "130237",</p><p>`    `"doctorId": "3608310",</p><p>`    `"deptId": "7155333",</p><p>`    `"patientInfo": {</p><p>`        `"gender": "男",</p><p>`        `"birthDate": "2000-03-15",</p><p>`        `"maritalStatus": 20,</p><p>`        `"pregnancyStatus": 0,</p><p>`        `"name": "张三"</p><p>`    `},</p><p>`    `"dataPacket": [</p><p>`        `{</p><p>`            `"tableCode": "b06\_1",</p><p>`            `"data": [</p><p>`                `{</p><p>`                    `"patient\_id": "00931222",</p><p>`                    `"visit\_sn": "00931222|4623477|1住院",</p><p>`                        `"record\_status": 1,</p><p>                    .... 剩余字段请参考 本文档《实时表清单》各场景相关字段说明 </p><p>`                `}</p><p>`            `]</p><p>`        `}</p><p>`    `],</p><p>`    `"extend1": "",</p><p>`    `"extend2": "",</p><p>`    `"extend3": "",</p><p>`    `"extend4": "",</p><p>`    `"extend5": "",</p><p>`    `"extend6": "",</p><p>`    `"extend7": "",</p><p>`    `"extend8": ""</p><p>}</p><p></p>|
| :- |
- 参数说明
  1. patient\_id		患者在本院唯一标识
  1. visit\_sn		患者本次就诊唯一标识
  1. record\_status	数据有效状态  1-有效数据  0-无效数据 （删除数据时,该字段值固定为0,其余为1）
  1. deptId 当前医生登录的科室id，不能为空,和离线采集的科室id一致，(同管理后台-医院配置-白名单配置中科室id一致)
  1. doctorId 为当前医生的id，不能为空，（同管理后台-医院配置-白名单配置中上传的医生id保持一致）
  1. patientInfo  可以不传(如果传了则patientInfo对象属性里maritalStatus 婚姻状况/ pregnancyStatus 妊娠状况字段必须为Integer类型，name姓名、gender性别为字符类型，birthDate格式为yyyy-MM-dd)
- 其余参数，根据b06\_1 表编码，参考附录内容
  1. ## <a name="_toc24674"></a>**出院记录**
- 传入必填参数

|<p>{</p><p>`    `"patientId": "80271534",</p><p>`    `"visitSn": "130237",</p><p>`    `"doctorId": "3608310",</p><p>`    `"deptId": "7155333",</p><p>`    `"patientInfo": {</p><p>`        `"gender": "男",</p><p>`        `"birthDate": "2000-03-15",</p><p>`        `"maritalStatus": 20,</p><p>`        `"pregnancyStatus": 0,</p><p>`        `"name": "张三"</p><p>`    `},</p><p>`    `"dataPacket": [</p><p>`        `{</p><p>`            `"tableCode": "b07\_1",</p><p>`            `"data": [</p><p>`                `{</p><p>`                    `"patient\_id": "00931222",</p><p>`                    `"visit\_sn": "00931222|4623477|1住院",</p><p>`                    `"record\_status": 1,</p><p>                    .... 剩余字段请参考 本文档《实时表清单》各场景相关字段说明</p><p>`                `}</p><p>`            `]</p><p>`        `},</p><p>`        `{</p><p>`            `"tableCode": "b07\_1\_1",</p><p>`            `"data": [</p><p>`                `{</p><p>`                    `"patient\_id": "00931222",</p><p>`                    `"visit\_sn": "00931222|4623477|1住院",</p><p>`                    `"record\_status": 1,</p><p>                    .... 剩余字段请参考 本文档《实时表清单》各场景相关字段说明</p><p>`                `}</p><p>`            `]</p><p>`        `}</p><p>`    `],</p><p>`    `"extend1": "",</p><p>`    `"extend2": "",</p><p>`    `"extend3": "",</p><p>`    `"extend4": "",</p><p>`    `"extend5": "",</p><p>`    `"extend6": "",</p><p>`    `"extend7": "",</p><p>`    `"extend8": ""</p><p>}</p><p></p>|
| :- |
- 参数说明
  1. patient\_id		患者在本院唯一标识
  1. visit\_sn		患者本次就诊唯一标识
  - record\_status	数据有效状态  1-有效数据  0-无效数据 （删除数据时,该字段值固定为0,其余为1）
  - deptId 当前医生登录的科室id，不能为空,和离线采集的科室id一致，(同管理后台-医院配置-白名单配置中科室id一致)
  - doctorId 为当前医生的id，不能为空，（同管理后台-医院配置-白名单配置中上传的医生id保持一致）
  - patientInfo  可以不传(如果传了则patientInfo对象属性里maritalStatus 婚姻状况/ pregnancyStatus 妊娠状况字段必须为Integer类型，name姓名、gender性别为字符类型，birthDate格式为yyyy-MM-dd)
  - 如果b07\_1\_1表无数据，可以不传
- 其余参数，根据b07\_1 / b07\_1\_1  表编码，参考附录内容

1. ## <a name="_toc6413"></a>**诊断**
- 传入必填参数

|<p>{</p><p>`    `"patientId": "80271534",</p><p>`    `"visitSn": "130237",</p><p>`    `"doctorId": "3608310",</p><p>`    `"deptId": "7155333",</p><p>`    `"patientInfo": {</p><p>`        `"gender": "男",</p><p>`        `"birthDate": "2000-03-15",</p><p>`        `"maritalStatus": 20,</p><p>`        `"pregnancyStatus": 0,</p><p>`        `"name": "张三"</p><p>`    `},</p><p>`    `"dataPacket": [</p><p>`        `{</p><p>`            `"tableCode": "b02\_3",</p><p>`            `"data": [</p><p>`                `{</p><p>`                    `"patient\_id": "00931222",</p><p>`                    `"visit\_sn": "00931222|4623477|1住院",</p><p>`                        `"record\_status": 1,</p><p>                    .... 剩余字段请参考 本文档《实时表清单》各场景相关字段说明 </p><p>`                `}</p><p>`            `]</p><p>`        `}</p><p>`    `],</p><p>`    `"extend1": "",</p><p>`    `"extend2": "",</p><p>`    `"extend3": "",</p><p>`    `"extend4": "",</p><p>`    `"extend5": "",</p><p>`    `"extend6": "",</p><p>`    `"extend7": "",</p><p>`    `"extend8": ""</p><p>}</p><p></p>|
| :- |
- 参数说明
  1. patient\_id		患者在本院唯一标识
  1. visit\_sn		患者本次就诊唯一标识
  1. record\_status	数据有效状态  1-有效数据  0-无效数据 （删除数据时,该字段值固定为0,其余为1）
  1. deptId 当前医生登录的科室id，不能为空,和离线采集的科室id一致，(同管理后台-医院配置-白名单配置中科室id一致)
  1. doctorId 为当前医生的id，不能为空，（同管理后台-医院配置-白名单配置中上传的医生id保持一致）
  1. patientInfo  可以不传(如果传了则patientInfo对象属性里maritalStatus 婚姻状况/ pregnancyStatus 妊娠状况字段必须为Integer类型，name姓名、gender性别为字符类型，birthDate格式为yyyy-MM-dd)
- 其余参数，根据b02\_3  表编码，参考附录内容

1. ## <a name="_toc26059"></a>**手术记录**
- 传入必填参数

|<p>{</p><p>`    `"patientId": "80271534",</p><p>`    `"visitSn": "130237",</p><p>`    `"doctorId": "3608310",</p><p>`    `"deptId": "7155333",</p><p>`    `"patientInfo": {</p><p>`        `"gender": "男",</p><p>`        `"birthDate": "2000-03-15",</p><p>`        `"maritalStatus": 20,</p><p>`        `"pregnancyStatus": 0,</p><p>`        `"name": "张三"</p><p>`    `},</p><p>`    `"dataPacket": [</p><p>`        `{</p><p>`            `"tableCode": "b08\_1",</p><p>`            `"data": [</p><p>`                `{</p><p>`                    `"patient\_id": "00931222",</p><p>`                    `"visit\_sn": "00931222|4623477|1住院",</p><p>`                    `"record\_status": 1,</p><p>                    .... 剩余字段请参考 本文档《实时表清单》各场景相关字段说明 </p><p>`                `}</p><p>`            `]</p><p>`        `},</p><p>`        `{</p><p>`            `"tableCode": "b08\_1\_1",</p><p>`            `"data": [</p><p>`                `{</p><p>`                    `"patient\_id": "00931222",</p><p>`                    `"visit\_sn": "00931222|4623477|1住院",</p><p>`                    `"record\_status": 1,</p><p>                    .... 剩余字段请参考 本文档《实时表清单》各场景相关字段说明 </p><p>`                `}</p><p>`            `]</p><p>`        `}</p><p>`    `],</p><p>`    `"extend1": "",</p><p>`    `"extend2": "",</p><p>`    `"extend3": "",</p><p>`    `"extend4": "",</p><p>`    `"extend5": "",</p><p>`    `"extend6": "",</p><p>`    `"extend7": "",</p><p>`    `"extend8": ""</p><p>}</p><p></p>|
| :- |
- 参数说明
  1. patient\_id		患者在本院唯一标识
  1. visit\_sn		患者本次就诊唯一标识
  1. record\_status	数据有效状态  1-有效数据  0-无效数据 （删除数据时,该字段值固定为0,其余为1）
  1. deptId 当前医生登录的科室id，不能为空,和离线采集的科室id一致，(同管理后台-医院配置-白名单配置中科室id一致)
  1. doctorId 为当前医生的id，不能为空，（同管理后台-医院配置-白名单配置中上传的医生id保持一致）
  1. patientInfo  可以不传(如果传了则patientInfo对象属性里maritalStatus 婚姻状况/ pregnancyStatus 妊娠状况字段必须为Integer类型，name姓名、gender性别为字符类型，birthDate格式为yyyy-MM-dd)
  1. 如果b08\_1\_1表无数据，可以不传
- 其余参数，根据b08\_1 / b08\_1\_1  表编码，参考附录内容
  1. ## <a name="_toc30928"></a>**24小时入出院记录**
- 传入必填参数

|<p>{</p><p>`    `"patientId": "80271534",</p><p>`    `"visitSn": "130237",</p><p>`    `"doctorId": "3608310",</p><p>`    `"deptId": "7155333",</p><p>`    `"patientInfo": {</p><p>`        `"gender": "男",</p><p>`        `"birthDate": "2000-03-15",</p><p>`        `"maritalStatus": 20,</p><p>`        `"pregnancyStatus": 0,</p><p>`        `"name": "张三"</p><p>`    `},</p><p>`    `"dataPacket": [</p><p>`        `{</p><p>`            `"tableCode": "b07\_2",</p><p>`            `"data": [</p><p>`                `{</p><p>`                    `"patient\_id": "00931222",</p><p>`                    `"visit\_sn": "00931222|4623477|1住院",</p><p>`                        `"record\_status": 1,</p><p>                    .... 剩余字段请参考 本文档《实时表清单》各场景相关字段说明 </p><p>`                `}</p><p>`            `]</p><p>`        `}</p><p>`    `],</p><p>`    `"extend1": "",</p><p>`    `"extend2": "",</p><p>`    `"extend3": "",</p><p>`    `"extend4": "",</p><p>`    `"extend5": "",</p><p>`    `"extend6": "",</p><p>`    `"extend7": "",</p><p>`    `"extend8": ""</p><p>}</p><p></p>|
| :- |
- 参数说明
  1. patient\_id		患者在本院唯一标识
  1. visit\_sn		患者本次就诊唯一标识
  1. record\_status	数据有效状态  1-有效数据  0-无效数据 （删除数据时,该字段值固定为0,其余为1）
  1. deptId 当前医生登录的科室id，不能为空,和离线采集的科室id一致，(同管理后台-医院配置-白名单配置中科室id一致)
  1. doctorId 为当前医生的id，不能为空，（同管理后台-医院配置-白名单配置中上传的医生id保持一致）
  1. patientInfo  可以不传(如果传了则patientInfo对象属性里maritalStatus 婚姻状况/ pregnancyStatus 妊娠状况字段必须为Integer类型，name姓名、gender性别为字符类型，birthDate格式为yyyy-MM-dd)
- 其余参数，根据b07\_2  表编码，参考附录内容


1. ## <a name="_toc6020"></a>**病案首页记录**
- 传入必填参数

|<p>{</p><p>`    `"patientId": "80271534",</p><p>`    `"visitSn": "130237",</p><p>`    `"doctorId": "3608310",</p><p>`    `"deptId": "7155333",</p><p>`    `"patientInfo": {</p><p>`        `"gender": "男",</p><p>`        `"birthDate": "2000-03-15",</p><p>`        `"maritalStatus": 20,</p><p>`        `"pregnancyStatus": 0,</p><p>`        `"name": "张三"</p><p>`    `},</p><p>`    `"dataPacket": [</p><p>`        `{</p><p>`            `"tableCode": "b04\_1",</p><p>`            `"data": [</p><p>`                `{</p><p>`                    `"patient\_id": "00931222",</p><p>`                    `"visit\_sn": "00931222|4623477|1住院",</p><p>`                    `"record\_status": 1,</p><p>                    .... 剩余字段请参考 本文档《实时表清单》各场景相关字段说明</p><p>`                `}</p><p>`            `]</p><p>`        `},</p><p>`        `{</p><p>`            `"tableCode": "b04\_1\_1",</p><p>`            `"data": [</p><p>`                `{</p><p>`                    `"patient\_id": "00931222",</p><p>`                    `"visit\_sn": "00931222|4623477|1住院",</p><p>`                    `"record\_status": 1,</p><p>                    .... 剩余字段请参考 本文档《实时表清单》各场景相关字段说明</p><p>`                `}</p><p>`            `]</p><p>`        `},</p><p>`        `{</p><p>`            `"tableCode": "b04\_1\_2",</p><p>`            `"data": [</p><p>`                `{</p><p>`                    `"patient\_id": "00931222",</p><p>`                    `"visit\_sn": "00931222|4623477|1住院",</p><p>`                    `"record\_status": 1,</p><p>                    .... 剩余字段请参考 本文档《实时表清单》各场景相关字段说明</p><p>`                `}</p><p>`            `]</p><p>`        `}</p><p>`    `],</p><p>`    `"extend1": "",</p><p>`    `"extend2": "",</p><p>`    `"extend3": "",</p><p>`    `"extend4": "",</p><p>`    `"extend5": "",</p><p>`    `"extend6": "",</p><p>`    `"extend7": "",</p><p>`    `"extend8": ""</p><p>}</p><p></p>|
| :- |
- 参数说明
  1. patient\_id		患者在本院唯一标识
  1. visit\_sn		患者本次就诊唯一标识
  1. record\_status	数据有效状态  1-有效数据  0-无效数据 （删除数据时,该字段值固定为0,其余为1）
  1. deptId 当前医生登录的科室id，不能为空,和离线采集的科室id一致，(同管理后台-医院配置-白名单配置中科室id一致)
  1. doctorId 为当前医生的id，不能为空，（同管理后台-医院配置-白名单配置中上传的医生id保持一致）
  1. patientInfo  可以不传(如果传了则patientInfo对象属性里maritalStatus 婚姻状况/ pregnancyStatus 妊娠状况字段必须为Integer类型，name姓名、gender性别为字符类型，birthDate格式为yyyy-MM-dd)
  1. 如果b04\_1\_1/b04\_1\_2表无数据，可以不传
  1. 其余参数，根据b04\_1/b04\_1\_1/b04\_1\_2表编码，参考附录内容

1. ## <a name="_toc16799"></a>**门诊医嘱**
- 传入必填参数

|<p>{</p><p>`    `"patientId": "80271534",</p><p>`    `"visitSn": "130237",</p><p>`    `"doctorId": "3608310",</p><p>`    `"deptId": "7155333",</p><p>`    `"patientInfo": {</p><p>`        `"gender": "男",</p><p>`        `"birthDate": "2000-03-15",</p><p>`        `"maritalStatus": 20,</p><p>`        `"pregnancyStatus": 0,</p><p>`        `"name": "张三"</p><p>`    `},</p><p>`    `"dataPacket": [</p><p>`        `{</p><p>`            `"tableCode": "b14\_1",</p><p>`            `"data": [</p><p>`                `{</p><p>`                    `"patient\_id": "00931222",</p><p>`                    `"visit\_sn": "00931222|4623477|1住院",</p><p>`                        `"record\_status": 1,</p><p>                    .... 剩余字段请参考 本文档《实时表清单》各场景相关字段说明 </p><p>`                `}</p><p>`            `]</p><p>`        `}</p><p>`    `],</p><p>`    `"extend1": "",</p><p>`    `"extend2": "",</p><p>`    `"extend3": "",</p><p>`    `"extend4": "",</p><p>`    `"extend5": "",</p><p>`    `"extend6": "",</p><p>`    `"extend7": "",</p><p>`    `"extend8": ""</p><p>}</p><p></p>|
| :- |
- 参数说明
  1. patient\_id		患者在本院唯一标识
  1. visit\_sn		患者本次就诊唯一标识
  1. record\_status	数据有效状态  1-有效数据  0-无效数据 （删除数据时,该字段值固定为0,其余为1）
  1. deptId 当前医生登录的科室id，不能为空,和离线采集的科室id一致，(同管理后台-医院配置-白名单配置中科室id一致)
  1. doctorId 为当前医生的id，不能为空，（同管理后台-医院配置-白名单配置中上传的医生id保持一致）
  1. patientInfo  可以不传(如果传了则patientInfo对象属性里maritalStatus 婚姻状况/ pregnancyStatus 妊娠状况字段必须为Integer类型，name姓名、gender性别为字符类型，birthDate格式为yyyy-MM-dd)
  1. 其余参数，根据b14\_1表编码，参考附录内容
  1. ## <a name="_toc24284"></a>**门诊申请单**
- 传入必填参数

|<p>{</p><p>`    `"patientId": "80271534",</p><p>`    `"visitSn": "130237",</p><p>`    `"doctorId": "3608310",</p><p>`    `"deptId": "7155333",</p><p>`    `"patientInfo": {</p><p>`        `"gender": "男",</p><p>`        `"birthDate": "2000-03-15",</p><p>`        `"maritalStatus": 20,</p><p>`        `"pregnancyStatus": 0,</p><p>`        `"name": "张三"</p><p>`    `},</p><p>`    `"dataPacket": [</p><p>`        `{</p><p>`            `"tableCode": "b14\_1",</p><p>`            `"data": [</p><p>`                `{</p><p>`                    `"patient\_id": "00931222",</p><p>`                    `"visit\_sn": "00931222|4623477|1住院",</p><p>`                        `"record\_status": 1,</p><p>                    .... 剩余字段请参考 本文档《实时表清单》各场景相关字段说明 </p><p>`                `}</p><p>`            `]</p><p>`        `}</p><p>`    `],</p><p>`    `"extend1": "",</p><p>`    `"extend2": "",</p><p>`    `"extend3": "",</p><p>`    `"extend4": "",</p><p>`    `"extend5": "",</p><p>`    `"extend6": "",</p><p>`    `"extend7": "",</p><p>`    `"extend8": ""</p><p>}</p><p></p>|
| :- |
- 参数说明
  1. patient\_id		患者在本院唯一标识
  1. visit\_sn		患者本次就诊唯一标识
  1. record\_status	数据有效状态  1-有效数据  0-无效数据 （删除数据时,该字段值固定为0,其余为1）
  1. deptId 当前医生登录的科室id，不能为空,和离线采集的科室id一致，(同管理后台-医院配置-白名单配置中科室id一致)
  1. doctorId 为当前医生的id，不能为空，（同管理后台-医院配置-白名单配置中上传的医生id保持一致）
  1. patientInfo  可以不传(如果传了则patientInfo对象属性里maritalStatus 婚姻状况/ pregnancyStatus 妊娠状况字段必须为Integer类型，name姓名、gender性别为字符类型，birthDate格式为yyyy-MM-dd)
- 其余参数，根据b14\_1表编码，参考附录内容
  1. ## <a name="_toc27112"></a>**门诊病历**
- 传入必填参数

|<p>{</p><p>`    `"patientId": "80271534",</p><p>`    `"visitSn": "130237",</p><p>`    `"doctorId": "3608310",</p><p>`    `"deptId": "7155333",</p><p>`    `"patientInfo": {</p><p>`        `"gender": "男",</p><p>`        `"birthDate": "2000-03-15",</p><p>`        `"maritalStatus": 20,</p><p>`        `"pregnancyStatus": 0,</p><p>`        `"name": "张三"</p><p>`    `},</p><p>`    `"dataPacket": [</p><p>`        `{</p><p>`            `"tableCode": "b13\_1",</p><p>`            `"data": [</p><p>`                `{</p><p>`                    `"patient\_id": "00931222",</p><p>`                    `"visit\_sn": "00931222|4623477|1住院",</p><p>`                        `"record\_status": 1,</p><p>                    .... 剩余字段请参考 本文档《实时表清单》各场景相关字段说明 </p><p>`                `}</p><p>`            `]</p><p>`        `},</p><p>{</p><p>`            `"tableCode": "b13\_1\_1",</p><p>`            `"data": [</p><p>`                `{</p><p>`                    `"patient\_id": "00931222",</p><p>`                    `"visit\_sn": "00931222|4623477|1住院",</p><p>`                        `"record\_status": 1,</p><p>                    .... 剩余字段请参考 本文档《实时表清单》各场景相关字段说明 </p><p>`                `}</p><p>`            `]</p><p>`        `}</p><p></p><p>`    `],</p><p>`    `"extend1": "",</p><p>`    `"extend2": "",</p><p>`    `"extend3": "",</p><p>`    `"extend4": "",</p><p>`    `"extend5": "",</p><p>`    `"extend6": "",</p><p>`    `"extend7": "",</p><p>`    `"extend8": ""</p><p>}</p><p></p>|
| :- |
- 参数说明
  1. patient\_id		患者在本院唯一标识
  1. visit\_sn		患者本次就诊唯一标识
  1. record\_status	数据有效状态  1-有效数据  0-无效数据 （删除数据时,该字段值固定为0,其余为1）
  1. deptId 当前医生登录的科室id，不能为空,和离线采集的科室id一致，(同管理后台-医院配置-白名单配置中科室id一致)
  1. doctorId 为当前医生的id，不能为空，（同管理后台-医院配置-白名单配置中上传的医生id保持一致）
  1. patientInfo  可以不传(如果传了则patientInfo对象属性里maritalStatus 婚姻状况/ pregnancyStatus 妊娠状况字段必须为Integer类型，name姓名、gender性别为字符类型，birthDate格式为yyyy-MM-dd)
  1. 如果b13\_1\_1表无数据，可不传
- 其余参数，根据b13\_1 / b13\_1\_1 表编码，参考附录内容
  1. ## <a name="_toc16426"></a>**病理报告**
- 传入必填参数

|<p>{</p><p>`    `"patientId": "80271534",</p><p>`    `"visitSn": "130237",</p><p>`    `"doctorId": "3608310",</p><p>`    `"deptId": "7155333",</p><p>`    `"patientInfo": {</p><p>`        `"gender": "男",</p><p>`        `"birthDate": "2000-03-15",</p><p>`        `"maritalStatus": 20,</p><p>`        `"pregnancyStatus": 0,</p><p>`        `"name": "张三"</p><p>`    `},</p><p>`    `"dataPacket": [</p><p>`        `{</p><p>`            `"tableCode": "b16\_2",</p><p>`            `"data": [</p><p>`                `{</p><p>`                    `"patient\_id": "00931222",</p><p>`                    `"visit\_sn": "00931222|4623477|1住院",</p><p>`                    `"record\_status": 1,</p><p>                    .... 剩余字段请参考 本文档《实时表清单》各场景相关字段说明 </p><p>`                `}</p><p>`            `]</p><p>`        `}</p><p>`            `]</p><p>`        `}</p><p>`    `],</p><p>`    `"extend1": "",</p><p>`    `"extend2": "",</p><p>`    `"extend3": "",</p><p>`    `"extend4": "",</p><p>`    `"extend5": "",</p><p>`    `"extend6": "",</p><p>`    `"extend7": "",</p><p>`    `"extend8": ""</p><p>}</p><p></p>|
| :- |
- 参数说明
  1. patient\_id		患者在本院唯一标识
  1. visit\_sn		患者本次就诊唯一标识
  1. record\_status	数据有效状态  1-有效数据  0-无效数据 （删除数据时,该字段值固定为0,其余为1）
  1. deptId 当前医生登录的科室id，不能为空,和离线采集的科室id一致，(同管理后台-医院配置-白名单配置中科室id一致)
  1. doctorId 为当前医生的id，不能为空，（同管理后台-医院配置-白名单配置中上传的医生id保持一致）
  1. patientInfo  可以不传(如果传了则patientInfo对象属性里maritalStatus 婚姻状况/ pregnancyStatus 妊娠状况字段必须为Integer类型，name姓名、gender性别为字符类型，birthDate格式为yyyy-MM-dd)
- 其余参数，根据b16\_2 表编码，参考附录内容
  1. ## **分子病理报告**
- 传入必填参数

|<p>{</p><p>`    `"patientId": "80271534",</p><p>`    `"visitSn": "130237",</p><p>`    `"doctorId": "3608310",</p><p>`    `"deptId": "7155333",</p><p>`    `"patientInfo": {</p><p>`        `"gender": "男",</p><p>`        `"birthDate": "2000-03-15",</p><p>`        `"maritalStatus": 20,</p><p>`        `"pregnancyStatus": 0,</p><p>`        `"name": "张三"</p><p>`    `},</p><p>`    `"dataPacket": [</p><p>`        `{</p><p>`            `"tableCode": "b16\_3",</p><p>`            `"data": [</p><p>`                `{</p><p>`                    `"patient\_id": "00931222",</p><p>`                    `"visit\_sn": "00931222|4623477|1住院",</p><p>`                    `"record\_status": 1,</p><p>                    .... 剩余字段请参考 本文档《实时表清单》各场景相关字段说明 </p><p>`                `}</p><p>`            `]</p><p>`        `}</p><p>`            `]</p><p>`        `}</p><p>`    `],</p><p>`    `"extend1": "",</p><p>`    `"extend2": "",</p><p>`    `"extend3": "",</p><p>`    `"extend4": "",</p><p>`    `"extend5": "",</p><p>`    `"extend6": "",</p><p>`    `"extend7": "",</p><p>`    `"extend8": ""</p><p>}</p><p></p>|
| :- |
- 参数说明
  1. patient\_id		患者在本院唯一标识
  1. visit\_sn		患者本次就诊唯一标识
  1. record\_status	数据有效状态  1-有效数据  0-无效数据 （删除数据时,该字段值固定为0,其余为1）
  1. deptId 当前医生登录的科室id，不能为空,和离线采集的科室id一致，(同管理后台-医院配置-白名单配置中科室id一致)
  1. doctorId 为当前医生的id，不能为空，（同管理后台-医院配置-白名单配置中上传的医生id保持一致）
  1. patientInfo  可以不传(如果传了则patientInfo对象属性里maritalStatus 婚姻状况/ pregnancyStatus 妊娠状况字段必须为Integer类型，name姓名、gender性别为字符类型，birthDate格式为yyyy-MM-dd)
- 其余参数，根据b16\_3 表编码，参考附录内容

1. ## **入院登记**
- 传入必填参数

|<p>{</p><p>`    `"patientId": "80271534",</p><p>`    `"visitSn": "130237",</p><p>`    `"doctorId": "3608310",</p><p>`    `"deptId": "7155333",</p><p>`    `"patientInfo": {</p><p>`        `"gender": "男",</p><p>`        `"birthDate": "2000-03-15",</p><p>`        `"maritalStatus": 20,</p><p>`        `"pregnancyStatus": 0,</p><p>`        `"name": "张三"</p><p>`    `},</p><p>`    `"dataPacket": [</p><p>`        `{</p><p>`            `"tableCode": "b02\_1",</p><p>`            `"data": [</p><p>`                `{</p><p>`                    `"patient\_id": "00931222",</p><p>`                    `"visit\_sn": "00931222|4623477|1住院",</p><p>`                    `"record\_status": 1,</p><p>                    .... 剩余字段请参考 本文档《实时表清单》各场景相关字段说明 </p><p>`                        `注意：入院登记包含的字段为b02\_1与b03\_2表字段取并集,</p><p>`                              `tableCode传入b02\_1</p><p>`                `}</p><p>`            `]</p><p>`        `}</p><p>`    `],</p><p>`    `"extend1": "",</p><p>`    `"extend2": "",</p><p>`    `"extend3": "",</p><p>`    `"extend4": "",</p><p>`    `"extend5": "",</p><p>`    `"extend6": "",</p><p>`    `"extend7": "",</p><p>`    `"extend8": ""</p><p>}</p><p></p>|
| :- |
- 参数说明
  1. patient\_id		患者在本院唯一标识
  1. visit\_sn		患者本次就诊唯一标识
  - record\_status	数据有效状态  1-有效数据  0-无效数据 （删除数据时,该字段值固定为0,其余为1）
  - deptId 当前医生登录的科室id，不能为空,和离线采集的科室id一致，(同管理后台-医院配置-白名单配置中科室id一致)
  - doctorId 为当前医生的id，不能为空，（同管理后台-医院配置-白名单配置中上传的医生id保持一致）
  - patientInfo  可以不传(如果传了则patientInfo对象属性里maritalStatus 婚姻状况/ pregnancyStatus 妊娠状况字段必须为Integer类型，name姓名、gender性别为字符类型，birthDate格式为yyyy-MM-dd)
- 其余参数，参考附录内容，取b02\_1和b03\_2表编码的字段合集。
  1. ## **检查报告**
- 传入必填参数

|<p>{</p><p>`    `"patientId": "80271534",</p><p>`    `"visitSn": "130237",</p><p>`    `"doctorId": "3608310",</p><p>`    `"deptId": "7155333",</p><p>`    `"patientInfo": {</p><p>`        `"gender": "男",</p><p>`        `"birthDate": "2000-03-15",</p><p>`        `"maritalStatus": 20,</p><p>`        `"pregnancyStatus": 0,</p><p>`        `"name": "张三"</p><p>`    `},</p><p>`    `"dataPacket": [</p><p>`        `{</p><p>`            `"tableCode": "b16\_1",</p><p>`            `"data": [</p><p>`                `{</p><p>`                    `"patient\_id": "00931222",</p><p>`                    `"visit\_sn": "00931222|4623477|1住院",</p><p>`                        `"record\_status": 1,</p><p>                    .... 剩余字段请参考 本文档《实时表清单》各场景相关字段说明 </p><p>`                `}</p><p>`            `]</p><p>`        `}</p><p>`    `],</p><p>`    `"extend1": "",</p><p>`    `"extend2": "",</p><p>`    `"extend3": "",</p><p>`    `"extend4": "",</p><p>`    `"extend5": "",</p><p>`    `"extend6": "",</p><p>`    `"extend7": "",</p><p>`    `"extend8": ""</p><p>}</p><p></p>|
| :- |
- 参数说明
  1. patient\_id		患者在本院唯一标识
  1. visit\_sn		患者本次就诊唯一标识
  1. record\_status	数据有效状态  1-有效数据  0-无效数据 （删除数据时,该字段值固定为0,其余为1）
  1. deptId 当前医生登录的科室id，不能为空,和离线采集的科室id一致，(同管理后台-医院配置-白名单配置中科室id一致)
  1. doctorId 为当前医生的id，不能为空，（同管理后台-医院配置-白名单配置中上传的医生id保持一致）
  1. patientInfo  可以不传(如果传了则patientInfo对象属性里maritalStatus 婚姻状况/ pregnancyStatus 妊娠状况字段必须为Integer类型，name姓名、gender性别为字符类型，birthDate格式为yyyy-MM-dd)
  1. 其余参数，根据b16\_1表编码，参考附录内容
  1. ## **门诊登记**
- 传入必填参数

|<p>{</p><p>`    `"patientId": "80271534",</p><p>`    `"visitSn": "130237",</p><p>`    `"doctorId": "3608310",</p><p>`    `"deptId": "7155333",</p><p>`    `"patientInfo": {</p><p>`        `"gender": "男",</p><p>`        `"birthDate": "2000-03-15",</p><p>`        `"maritalStatus": 20,</p><p>`        `"pregnancyStatus": 0,</p><p>`        `"name": "张三"</p><p>`    `},</p><p>`    `"dataPacket": [</p><p>`        `{</p><p>`            `"tableCode": "b12\_1",</p><p>`            `"data": [</p><p>`                `{</p><p>`                    `"patient\_id": "00931222",</p><p>`                    `"visit\_sn": "00931222|4623478|1门诊",</p><p>                    .... 剩余字段请参考 本文档《实时表清单》各场景相关字段说明 </p><p>`                        `注意：此场景传递的字段需要是b12\_1 和 b02\_1 字段的合集</p><p></p><p>`                `}</p><p>`            `]</p><p>`        `}</p><p>`    `],</p><p>`    `"extend1": "",</p><p>`    `"extend2": "",</p><p>`    `"extend3": "",</p><p>`    `"extend4": "",</p><p>`    `"extend5": "",</p><p>`    `"extend6": "",</p><p>`    `"extend7": "",</p><p>`    `"extend8": ""</p><p>}</p><p></p>|
| :- |
||
||
- 参数说明
  1. patient\_id		患者在本院唯一标识
  1. visit\_sn		患者本次就诊唯一标识
  1. record\_status	数据有效状态  1-有效数据  0-无效数据 （删除数据时,该字段值固定为0,其余为1）
  1. deptId 当前医生登录的科室id，不能为空,和离线采集的科室id一致，(同管理后台-医院配置-白名单配置中科室id一致)
  1. doctorId 为当前医生的id，不能为空，（同管理后台-医院配置-白名单配置中上传的医生id保持一致）
  1. patientInfo  可以不传(如果传了则patientInfo对象属性里maritalStatus 婚姻状况/ pregnancyStatus 妊娠状况字段必须为Integer类型，name姓名、gender性别为字符类型，birthDate格式为yyyy-MM-dd)
- 其余参数，参考附录内容，取b02\_1和b12\_1表编码的字段合集。
  1. ## **检验报告**
- 传入必填参数

|<p>{</p><p>`    `"patientId": "80271534",</p><p>`    `"visitSn": "130237",</p><p>`    `"doctorId": "3608310",</p><p>`    `"deptId": "7155333",</p><p>`    `"patientInfo": {</p><p>`        `"gender": "男",</p><p>`        `"birthDate": "2000-03-15",</p><p>`        `"maritalStatus": 20,</p><p>`        `"pregnancyStatus": 0,</p><p>`        `"name": "张三"</p><p>`    `},</p><p>`    `"dataPacket": [</p><p>`        `{</p><p>`            `"tableCode": "b17\_1",</p><p>`            `"data": [</p><p>`                `{</p><p>`                    `"patient\_id": "00931222",</p><p>`                    `"visit\_sn": "00931222|4623478|1门诊",</p><p>`                    `"record\_status": 1,</p><p>                    .... 剩余字段请参考 本文档《实时表清单》各场景相关字段说明 </p><p>`                `}</p><p>`            `]</p><p>`        `},</p><p>`        `{</p><p>`            `"tableCode": "b17\_1\_1",</p><p>`            `"data": [</p><p>`                `{</p><p>`                    `"patient\_id": "00931222",</p><p>`                    `"visit\_sn": "00931222|4623478|1门诊",</p><p>`                    `"record\_status": 1,</p><p>                    .... 剩余字段请参考 本文档《实时表清单》各场景相关字段说明 </p><p>`                `}</p><p>`            `]</p><p>`        `}</p><p>`    `],</p><p>`    `"extend1": "",</p><p>`    `"extend2": "",</p><p>`    `"extend3": "",</p><p>`    `"extend4": "",</p><p>`    `"extend5": "",</p><p>`    `"extend6": "",</p><p>`    `"extend7": "",</p><p>`    `"extend8": ""</p><p>}</p><p></p>|
| :- |
- 参数说明
  1. patient\_id		患者在本院唯一标识
  1. visit\_sn		患者本次就诊唯一标识
  1. record\_status	数据有效状态  1-有效数据  0-无效数据 （删除数据时,该字段值固定为0,其余为1）
  1. deptId 当前医生登录的科室id，不能为空,和离线采集的科室id一致，(同管理后台-医院配置-白名单配置中科室id一致)
  1. doctorId 为当前医生的id，不能为空，（同管理后台-医院配置-白名单配置中上传的医生id保持一致）
  1. patientInfo  可以不传(如果传了则patientInfo对象属性里maritalStatus 婚姻状况/ pregnancyStatus 妊娠状况字段必须为Integer类型，name姓名、gender性别为字符类型，birthDate格式为yyyy-MM-dd)
  1. 如果b17\_1\_1表无数据，可不传
- 其余参数，根据b17\_1 / b17\_1\_1  表编码，参考附录内容
  1. ## **体检报告**
- 传入必填参数

|<p>{</p><p>`    `"patientId": "80271534",</p><p>`    `"visitSn": "130237",</p><p>`    `"doctorId": "3608310",</p><p>`    `"deptId": "7155333",</p><p>`    `"patientInfo": {</p><p>`        `"gender": "男",</p><p>`        `"birthDate": "2000-03-15",</p><p>`        `"maritalStatus": 20,</p><p>`        `"pregnancyStatus": 0,</p><p>`        `"name": "张三"</p><p>`    `},</p><p>`    `"dataPacket": [</p><p>`        `{</p><p>`            `"tableCode": "b22\_1",</p><p>`            `"data": [</p><p>`                `{</p><p>`                    `"patient\_id": "80271534",</p><p>`                    `"visit\_sn": "00931222|4623478|1门诊",</p><p>`                        `"record\_status": 1,</p><p>                    .... 剩余字段请参考 本文档《实时表清单》各场景相关字段说明 </p><p>`                `}</p><p>`            `]</p><p>`        `}</p><p>`    `],</p><p>`    `"extend1": "",</p><p>`    `"extend2": "",</p><p>`    `"extend3": "",</p><p>`    `"extend4": "",</p><p>`    `"extend5": "",</p><p>`    `"extend6": "",</p><p>`    `"extend7": "",</p><p>`    `"extend8": ""</p><p>}</p><p></p>|
| :- |
- 参数说明
  1. patient\_id		患者在本院唯一标识
  1. visit\_sn		患者本次就诊唯一标识
  1. record\_status	数据有效状态  1-有效数据  0-无效数据 （删除数据时,该字段值固定为0,其余为1）
  1. deptId 当前医生登录的科室id，不能为空,和离线采集的科室id一致，(同管理后台-医院配置-白名单配置中科室id一致)
  1. doctorId 为当前医生的id，不能为空，（同管理后台-医院配置-白名单配置中上传的医生id保持一致）
  1. patientInfo  可以不传(如果传了则patientInfo对象属性里maritalStatus 婚姻状况/ pregnancyStatus 妊娠状况字段必须为Integer类型，name姓名、gender性别为字符类型，birthDate格式为yyyy-MM-dd)
  1. 其余参数，根据b22\_1表编码，参考附录内容

1. ## **DRG数据上报**
- 传入必填参数

|<p>{</p><p>`    `"patientId": "80271534",</p><p>`    `"visitSn": "130237",</p><p>`    `"doctorId": "3608310",</p><p>`    `"deptId": "7155333",</p><p>`    `"patientInfo": {</p><p>`        `"gender": "男",</p><p>`        `"birthDate": "2000-03-15",</p><p>`        `"maritalStatus": 20,</p><p>`        `"pregnancyStatus": 0,</p><p>`        `"name": "张三"</p><p>`    `},</p><p>`    `"dataPacket": [</p><p>`        `{</p><p>`            `"tableCode": "aggr\_basefee",</p><p>`            `"data": [</p><p>`                `{</p><p>`                    `"patient\_id": "80271534",</p><p>`                    `"visit\_sn": "130237",</p><p>`                        `"record\_status": 1,</p><p>                    .... 剩余字段请参考 本文档《实时表清单》各场景相关字段说明 </p><p>`                `}</p><p>`            `]</p><p>`        `}</p><p>`    `],</p><p>`    `"extend1": "",</p><p>`    `"extend2": "",</p><p>`    `"extend3": "",</p><p>`    `"extend4": "",</p><p>`    `"extend5": "",</p><p>`    `"extend6": "",</p><p>`    `"extend7": "",</p><p>`    `"extend8": ""</p><p>}</p><p></p>|
| :- |
- 参数说明
  1. patient\_id		患者在本院唯一标识
  1. visit\_sn		患者本次就诊唯一标识
  1. record\_status	数据有效状态  1-有效数据  0-无效数据 （删除数据时,该字段值固定为0,其余为1）
  1. deptId 当前医生登录的科室id，原则上不能为空,和离线采集的科室id一致，(同管理后台-医院配置-白名单配置中科室id一致)
  1. doctorId 为当前医生的id，原则上不能为空，（同管理后台-医院配置-白名单配置中上传的医生id保持一致）
  1. patientInfo  可以不传(如果传了则patientInfo对象属性里maritalStatus 婚姻状况/ pregnancyStatus 妊娠状况字段必须为Integer类型，name姓名、gender性别为字符类型，birthDate格式为yyyy-MM-dd)
  1. 其余参数，根据aggr\_basefee表编码，参考附录内容

