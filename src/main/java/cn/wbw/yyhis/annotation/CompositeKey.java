package cn.wbw.yyhis.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 联合主键注解
 * 用于标记实体类字段作为业务上的联合主键，在删除操作时会根据此注解提取字段值构造删除数据
 * 
 * <AUTHOR>
 * @since 2024-07-13
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface CompositeKey {
    
    /**
     * 联合主键的顺序，用于排序
     * @return 顺序值，数字越小优先级越高
     */
    int order() default 0;
    
    /**
     * 字段在删除请求中的名称，如果不指定则使用字段名
     * @return 删除请求中的字段名
     */
    String name() default "";
    
    /**
     * 是否必填，如果为true且字段值为null，则会抛出异常
     * @return 是否必填
     */
    boolean required() default true;
}
