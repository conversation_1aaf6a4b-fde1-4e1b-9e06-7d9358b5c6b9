package cn.wbw.yyhis.config;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;

import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 自定义序列化器，将对象的字段名从驼峰命名转换为下划线命名
 * 支持单个对象和List对象的序列化
 */
public class SnakeCaseListSerializer extends JsonSerializer<Object> {

    private static final String DATE_FORMAT = "yyyy-MM-dd";
    private static final String DATETIME_FORMAT = "yyyy-MM-dd HH:mm:ss";

    private static final ObjectMapper SNAKE_CASE_MAPPER = createSnakeCaseMapper();

    private static ObjectMapper createSnakeCaseMapper() {
        ObjectMapper mapper = new ObjectMapper();
        mapper.setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE);

        // 配置Java 8时间模块
        JavaTimeModule javaTimeModule = new JavaTimeModule();
        javaTimeModule.addSerializer(LocalDateTime.class, new LocalDateTimeSerializer(DateTimeFormatter.ofPattern(DATETIME_FORMAT)));
        javaTimeModule.addDeserializer(LocalDateTime.class, new LocalDateTimeDeserializer(DateTimeFormatter.ofPattern(DATETIME_FORMAT)));
        javaTimeModule.addSerializer(LocalDate.class, new LocalDateSerializer(DateTimeFormatter.ofPattern(DATE_FORMAT)));
        javaTimeModule.addDeserializer(LocalDate.class, new LocalDateDeserializer(DateTimeFormatter.ofPattern(DATE_FORMAT)));

        mapper.registerModule(javaTimeModule);
        return mapper;
    }

    @Override
    public void serialize(Object value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        if (value == null) {
            gen.writeNull();
            return;
        }

        // 判断是否为List类型
        if (value instanceof List<?>) {
            List<?> list = (List<?>) value;
            gen.writeStartArray();
            for (Object item : list) {
                if (item == null) {
                    gen.writeNull();
                } else {
                    // 使用配置了下划线命名策略的ObjectMapper来序列化每个对象
                    String json = SNAKE_CASE_MAPPER.writeValueAsString(item);
                    gen.writeRawValue(json);
                }
            }
            gen.writeEndArray();
        } else {
            // 单个对象序列化
            String json = SNAKE_CASE_MAPPER.writeValueAsString(value);
            gen.writeRawValue(json);
        }
    }
}
