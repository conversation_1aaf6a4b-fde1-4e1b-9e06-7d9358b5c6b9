package cn.wbw.yyhis.util;

import cn.wbw.yyhis.util.model.ColumnDefinition;
import cn.wbw.yyhis.util.model.TableDefinition;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.FileInputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class SchemaGenerator {

    // 定义Excel列索引的常量，增强可读性和可维护性
    private static final int COL_TABLE_NAME = 0;
    private static final int COL_COLUMN_NAME = 1;
    private static final int COL_COLUMN_CN_NAME = 2;
    private static final int COL_DESCRIPTION = 4;
    private static final int COL_DATA_TYPE = 7;
    private static final int COL_IS_PK = 8;
    private static final int COL_IS_REQUIRED = 9;
    private static final int COL_IS_COMPOSITE_PK = 10;

    // MySQL一行所有varchar加起来不能超过65535字节，这里用一个保守值
    private static final int MAX_VARCHAR_BYTES_SUM = 65000;

    public Map<String, TableDefinition> parseExcel(String filePath) throws IOException {
        // 使用LinkedHashMap保持插入顺序，使生成的SQL文件中的表顺序与Excel中首次出现的顺序一致
        Map<String, TableDefinition> tables = new LinkedHashMap<>();

        try (FileInputStream fis = new FileInputStream(filePath);
             Workbook workbook = new XSSFWorkbook(fis)) {

            Sheet sheet = workbook.getSheetAt(0);
            // 跳过标题行，从第二行开始遍历
            for (int i = 1; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                if (row == null) {
                    continue;
                }

                String tableName = getCellValue(row.getCell(COL_TABLE_NAME));
                if (tableName == null || tableName.trim().isEmpty()) {
                    continue; // 如果表名为空，则跳过此行
                }

                TableDefinition tableDef = tables.computeIfAbsent(tableName, k -> {
                    TableDefinition newTable = new TableDefinition();
                    newTable.setName(k);
                    return newTable;
                });

                ColumnDefinition columnDef = new ColumnDefinition();
                columnDef.setName(getCellValue(row.getCell(COL_COLUMN_NAME)));

                String rawType = getCellValue(row.getCell(COL_DATA_TYPE));
                String normalizedType = normalizeDataType(rawType);
                columnDef.setType(normalizedType); // Store normalized type first
                
                String chineseName = getCellValue(row.getCell(COL_COLUMN_CN_NAME));
                String description = getCellValue(row.getCell(COL_DESCRIPTION));
                columnDef.setComment(buildComment(chineseName, description));

                columnDef.setNotNull("必填".equals(getCellValue(row.getCell(COL_IS_REQUIRED))));
                
                boolean isPk = "是".equals(getCellValue(row.getCell(COL_IS_PK)));
                boolean isCompositePk = "是".equals(getCellValue(row.getCell(COL_IS_COMPOSITE_PK)));
                
                columnDef.setPrimaryKey(isPk);
                columnDef.setCompositePrimaryKey(isCompositePk);

                tableDef.getColumns().add(columnDef);
            }
        }
        return tables;
    }

    private String getCellValue(Cell cell) {
        if (cell == null) {
            return "";
        }
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                // 对于数字，统一转换为字符串处理
                return String.valueOf(cell.getNumericCellValue());
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                // 如果是公式，尝试获取计算后的值
                try {
                    return cell.getStringCellValue();
                } catch (IllegalStateException e) {
                    return String.valueOf(cell.getNumericCellValue());
                }
            default:
                return "";
        }
    }
    
    private String buildComment(String chineseName, String description) {
        StringBuilder comment = new StringBuilder();
        if (chineseName != null && !chineseName.trim().isEmpty()) {
            comment.append(chineseName);
        }
        if (description != null && !description.trim().isEmpty()) {
            if (comment.length() > 0) {
                comment.append(" - ");
            }
            comment.append(description);
        }
        return comment.toString().replace("'", "''");
    }

    private int calculateTypeBytes(String type) {
        if (type == null || type.isEmpty()) return 0;
        String lowerType = type.toLowerCase().trim();

        // 不计入行大小限制的类型
        if (lowerType.contains("text") || lowerType.contains("blob")) {
            return 0;
        }

        // Varchar: N * 4 + 2 (utf8mb4)
        if (lowerType.startsWith("varchar")) {
            Pattern p = Pattern.compile("\\((\\d+)\\)");
            Matcher m = p.matcher(lowerType);
            if (m.find()) {
                int len = Integer.parseInt(m.group(1));
                return len * 4 + 2;
            }
        }

        // Char: N * 4 (utf8mb4)
        if (lowerType.startsWith("char")) {
            Pattern p = Pattern.compile("\\((\\d+)\\)");
            Matcher m = p.matcher(lowerType);
            if (m.find()) {
                int len = Integer.parseInt(m.group(1));
                return len * 4;
            }
        }

        // 固定长度类型
        if (lowerType.startsWith("int")) return 4;
        if (lowerType.startsWith("bigint")) return 8;
        if (lowerType.startsWith("datetime")) return 8;
        if (lowerType.startsWith("date")) return 3;
        if (lowerType.startsWith("timestamp")) return 4;
        if (lowerType.startsWith("double")) return 8;
        if (lowerType.startsWith("float")) return 4;
        if (lowerType.startsWith("tinyint")) return 1;
        if (lowerType.startsWith("smallint")) return 2;
        if (lowerType.startsWith("mediumint")) return 3;

        // 其他类型暂不精确计算，可按需扩展
        return 0;
    }

    /**
     * 规范化和清理从Excel读取的数据类型字符串。
     * - 将中文括号替换为英文括号。
     * - 修正 "vachar" 拼写错误为 "varchar" (不区分大小写)。
     * @param dataType 原始数据类型字符串
     * @return 清理后的数据类型字符串
     */
    private String normalizeDataType(String dataType) {
        if (dataType == null || dataType.trim().isEmpty()) {
            return "";
        }
        // 替换中文括号
        String normalized = dataType.replace('（', '(').replace('）', ')');
        // 修正拼写错误（不区分大小写）
        normalized = normalized.replaceAll("(?i)vachar", "varchar");
        normalized = normalized.replaceAll("(?i)bight", "bigint");
        normalized = normalized.replace("bigint(100)", "bigint(20)");
        return normalized;
    }

    /**
     * 遍历所有解析的表，并根据规则确定其主键。
     * 规则：如果一个表有任何列被标记为"主键"，则这些列构成主键。
     * 否则，所有被标记为"联合主键"的列将共同构成主键。
     * @param tables 从Excel解析出的表定义Map
     */
    public void determinePrimaryKeys(Map<String, TableDefinition> tables) {
        for (TableDefinition tableDef : tables.values()) {
            List<String> singlePks = tableDef.getColumns().stream()
                    .filter(ColumnDefinition::isPrimaryKey)
                    .map(ColumnDefinition::getName)
                    .toList();

            if (!singlePks.isEmpty()) {
                // 优先使用"主键"列
                tableDef.setPrimaryKeyColumns(singlePks);
            } else {
                // 如果没有"主键"列，则使用"联合主键"列
                List<String> compositePks = tableDef.getColumns().stream()
                        .filter(ColumnDefinition::isCompositePrimaryKey)
                        .map(ColumnDefinition::getName)
                        .toList();
                tableDef.setPrimaryKeyColumns(compositePks);
            }
        }
    }

    public String generateSql(Map<String, TableDefinition> tables) {
        StringBuilder sql = new StringBuilder();

        for (TableDefinition table : tables.values()) {
            sql.append("-- ----------------------------\n");
            sql.append("-- Table structure for ").append(table.getName()).append("\n");
            sql.append("-- ----------------------------\n");
            sql.append("DROP TABLE IF EXISTS `").append(table.getName()).append("`;\n");
            sql.append("CREATE TABLE `").append(table.getName()).append("` (\n");

            for (int i = 0; i < table.getColumns().size(); i++) {
                ColumnDefinition column = table.getColumns().get(i);
                sql.append("  `").append(column.getName()).append("` ").append(column.getType());
                if (column.isNotNull()) {
                    sql.append(" NOT NULL");
                }
                if (column.getComment() != null && !column.getComment().isEmpty()) {
                    sql.append(" COMMENT '").append(column.getComment()).append("'");
                }
                // 如果不是最后一列或者有主键定义，则添加逗号
                if (i < table.getColumns().size() - 1 || !table.getPrimaryKeyColumns().isEmpty()) {
                    sql.append(",");
                }
                sql.append("\n");
            }

            if (!table.getPrimaryKeyColumns().isEmpty()) {
                sql.append("  PRIMARY KEY (");
                for (int i = 0; i < table.getPrimaryKeyColumns().size(); i++) {
                    sql.append("`").append(table.getPrimaryKeyColumns().get(i)).append("`");
                    if (i < table.getPrimaryKeyColumns().size() - 1) {
                        sql.append(", ");
                    }
                }
                sql.append(") USING BTREE\n");
            }

            // 获取表注释，如果没有特定注释，则使用表名
            String tableComment = (table.getComment() != null && !table.getComment().isEmpty())
                    ? table.getComment()
                    : table.getName();

            sql.append(") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='")
               .append(tableComment)
               .append("';\n\n");
        }

        return sql.toString();
    }

    private void processTablesForVarcharConversion(Map<String, TableDefinition> tables) {
        System.out.println("开始对每个表独立计算Varchar长度阈值...");

        for (TableDefinition table : tables.values()) {
            // 1. 为当前表收集varchar长度并降序排序
            List<Integer> localVarcharLengths = table.getColumns().stream()
                    .map(c -> {
                        String type = c.getType().toLowerCase();
                        if (type.startsWith("varchar")) {
                            Matcher m = Pattern.compile("\\((\\d+)\\)").matcher(type);
                            if (m.find()) return Integer.parseInt(m.group(1));
                        }
                        return -1;
                    })
                    .filter(l -> l > 0)
                    .distinct()
                    .sorted(Comparator.reverseOrder())
                    .toList();

            if (localVarcharLengths.isEmpty()) {
                // 此表没有varchar，无需处理
                continue;
            }

            // 2. 计算此表中非varchar列的总大小
            int nonVarcharBaseSize = table.getColumns().stream()
                    .filter(c -> !c.getType().toLowerCase().startsWith("varchar"))
                    .mapToInt(c -> calculateTypeBytes(c.getType()))
                    .sum();

            // 3. 为此表寻找最佳阈值
            int localThreshold = -1;
            for (int potentialThreshold : localVarcharLengths) {
                int hypotheticalVarcharSize = table.getColumns().stream()
                        .filter(c -> c.getType().toLowerCase().startsWith("varchar"))
                        .mapToInt(c -> {
                            Matcher m = Pattern.compile("\\((\\d+)\\)").matcher(c.getType().toLowerCase());
                            if (m.find()) {
                                if (Integer.parseInt(m.group(1)) <= potentialThreshold) {
                                    return calculateTypeBytes(c.getType());
                                }
                            }
                            return 0; // 大于阈值的varchar被视为text，不计大小
                        }).sum();

                if (nonVarcharBaseSize + hypotheticalVarcharSize <= MAX_VARCHAR_BYTES_SUM) {
                    localThreshold = potentialThreshold;
                    break; // 找到第一个安全的阈值，即为此表的最佳阈值
                }
            }

            if (localThreshold == -1) {
                // 如果即使最小的varchar也会导致超限，则此表所有varchar都需转换
                localThreshold = 0;
            }

            // 4. 应用此表的专属阈值
            for (ColumnDefinition column : table.getColumns()) {
                String type = column.getType().toLowerCase();
                if (type.startsWith("varchar")) {
                    Matcher m = Pattern.compile("\\((\\d+)\\)").matcher(type);
                    if (m.find() && Integer.parseInt(m.group(1)) > localThreshold) {
                        column.setType("text");
                    }
                }
            }
            System.out.println("表 '" + table.getName() + "' 处理完成，计算出的专属Varchar阈值为: " + localThreshold);
        }
        System.out.println("所有表的Varchar转换处理完成。");
    }

    public static void main(String[] args) {
        String excelFilePath = "src/main/resources/templates/字段目录.xlsx";
        String outputSqlFilePath = "src/main/resources/db/schema.sql";

        SchemaGenerator generator = new SchemaGenerator();
        try {
            System.out.println("开始解析Excel文件: " + excelFilePath);
            Map<String, TableDefinition> tables = generator.parseExcel(excelFilePath);
            System.out.println("Excel文件解析完成，共找到 " + tables.size() + " 个表。");

            // 应用新的"逐表独立计算"法
            generator.processTablesForVarcharConversion(tables);

            System.out.println("正在根据规则确定主键...");
            generator.determinePrimaryKeys(tables);
            System.out.println("主键确定完成。");

            System.out.println("开始生成SQL建表语句...");
            String sqlScript = generator.generateSql(tables);
            System.out.println("SQL建表语句生成完成。");

            // 确保目录存在
            Files.createDirectories(Paths.get(outputSqlFilePath).getParent());
            // 使用NIO写入文件，确保UTF-8编码
            Files.write(Paths.get(outputSqlFilePath), sqlScript.getBytes("UTF-8"),
                    StandardOpenOption.CREATE, StandardOpenOption.TRUNCATE_EXISTING);

            System.out.println("成功将SQL脚本写入到文件: " + outputSqlFilePath);

        } catch (IOException e) {
            System.err.println("处理过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
} 