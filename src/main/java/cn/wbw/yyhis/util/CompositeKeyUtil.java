package cn.wbw.yyhis.util;

import cn.wbw.yyhis.annotation.CompositeKey;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 联合主键工具类
 * 用于根据@CompositeKey注解提取实体对象的联合主键字段值
 * 
 * <AUTHOR>
 * @since 2024-07-13
 */
public class CompositeKeyUtil {
    
    private static final Logger log = LoggerFactory.getLogger(CompositeKeyUtil.class);
    
    /**
     * 从单个实体对象中提取联合主键数据
     * 
     * @param entity 实体对象
     * @return 包含联合主键字段的Map
     */
    public static Map<String, Object> extractCompositeKeyData(Object entity) {
        if (entity == null) {
            return new HashMap<>();
        }
        
        List<Map<String, Object>> dataList = extractCompositeKeyDataList(Collections.singletonList(entity));
        return dataList.isEmpty() ? new HashMap<>() : dataList.get(0);
    }
    
    /**
     * 从实体对象列表中提取联合主键数据列表
     * 
     * @param entities 实体对象列表
     * @return 包含联合主键字段的Map列表
     */
    public static List<Map<String, Object>> extractCompositeKeyDataList(List<?> entities) {
        if (entities == null || entities.isEmpty()) {
            return new ArrayList<>();
        }
        
        List<Map<String, Object>> result = new ArrayList<>();
        
        for (Object entity : entities) {
            if (entity == null) {
                continue;
            }
            
            Map<String, Object> keyData = extractFromSingleEntity(entity);
            if (!keyData.isEmpty()) {
                result.add(keyData);
            }
        }
        
        return result;
    }
    
    /**
     * 从单个实体对象中提取联合主键字段
     * 
     * @param entity 实体对象
     * @return 联合主键字段Map
     */
    private static Map<String, Object> extractFromSingleEntity(Object entity) {
        Map<String, Object> keyData = new LinkedHashMap<>();
        
        try {
            Class<?> entityClass = entity.getClass();
            
            // 获取所有标记了@CompositeKey注解的字段
            List<Field> compositeKeyFields = getCompositeKeyFields(entityClass);
            
            if (compositeKeyFields.isEmpty()) {
                log.warn("实体类 {} 没有标记@CompositeKey注解的字段", entityClass.getSimpleName());
                return keyData;
            }
            
            // 按order排序
            compositeKeyFields.sort(Comparator.comparingInt(field -> field.getAnnotation(CompositeKey.class).order()));
            
            // 提取字段值
            for (Field field : compositeKeyFields) {
                CompositeKey annotation = field.getAnnotation(CompositeKey.class);
                field.setAccessible(true);
                
                Object value = field.get(entity);
                
                // 检查必填字段
                // if (annotation.required() && value == null) {
                //     throw new IllegalArgumentException(
                //         String.format("联合主键字段 %s.%s 为必填字段，但值为null",
                //             entityClass.getSimpleName(), field.getName()));
                // }
                
                // 确定字段在删除请求中的名称
                String keyName = StringUtils.hasText(annotation.name()) ? annotation.name() : field.getName();
                
                keyData.put(keyName, value);
            }
            
            log.debug("从实体 {} 提取联合主键数据: {}", entityClass.getSimpleName(), keyData);
            
        } catch (IllegalAccessException e) {
            log.error("提取联合主键数据时发生异常", e);
            throw new RuntimeException("提取联合主键数据失败", e);
        }
        
        return keyData;
    }
    
    /**
     * 获取类中所有标记了@CompositeKey注解的字段
     * 
     * @param clazz 实体类
     * @return 标记了@CompositeKey注解的字段列表
     */
    private static List<Field> getCompositeKeyFields(Class<?> clazz) {
        List<Field> compositeKeyFields = new ArrayList<>();
        
        // 遍历当前类及其父类的所有字段
        Class<?> currentClass = clazz;
        while (currentClass != null && currentClass != Object.class) {
            Field[] fields = currentClass.getDeclaredFields();
            
            for (Field field : fields) {
                if (field.isAnnotationPresent(CompositeKey.class)) {
                    compositeKeyFields.add(field);
                }
            }
            
            currentClass = currentClass.getSuperclass();
        }
        
        return compositeKeyFields;
    }
    
    /**
     * 检查实体类是否有@CompositeKey注解的字段
     * 
     * @param clazz 实体类
     * @return 是否有@CompositeKey注解的字段
     */
    public static boolean hasCompositeKeyFields(Class<?> clazz) {
        return !getCompositeKeyFields(clazz).isEmpty();
    }
}
