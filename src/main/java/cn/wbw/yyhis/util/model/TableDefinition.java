package cn.wbw.yyhis.util.model;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 表示数据库表的定义
 */
@Data
public class TableDefinition {

    /**
     * 表单名称
     */
    private String name;

    /**
     * 表注释 (可从第一行列的说明中提取)
     */
    private String comment;

    /**
     * 表包含的列定义列表
     */
    private List<ColumnDefinition> columns = new ArrayList<>();

    /**
     * 主键列的名称列表 (用于处理联合主键)
     */
    private List<String> primaryKeyColumns = new ArrayList<>();

    /**
     * 用于追踪表中所有列累加的字节数，以防止超出MySQL行大小限制。
     * 这是一个瞬态字段，仅在生成过程中使用。
     */
    private transient int accumulatedRowBytes = 0;
} 