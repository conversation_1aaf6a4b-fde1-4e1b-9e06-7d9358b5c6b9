package cn.wbw.yyhis.util.model;

import lombok.Data;

/**
 * 表示数据库列的定义
 */
@Data
public class ColumnDefinition {

    /**
     * 字段英文名
     */
    private String name;

    /**
     * 字段类型及长度
     */
    private String type;

    /**
     * 是否必填 (NOT NULL)
     */
    private boolean isNotNull;

    /**
     * 字段中文名和说明，将作为注释
     */
    private String comment;

    /**
     * 是否是单主键
     */
    private boolean isPrimaryKey;

    /**
     * 是否是联合主键的一部分
     */
    private boolean isCompositePrimaryKey;

} 