package cn.wbw.yyhis.util;

import cn.wbw.yyhis.YyhisServerApplication;
import cn.wbw.yyhis.model.entity.SystemDictData;
import cn.wbw.yyhis.service.SystemDictDataService;
import org.springframework.boot.SpringApplication;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.io.ClassPathResource;

import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * TNM数据导入工具类
 * 用于导入临床TNM20250723.xlsx文件到SystemDictData表
 * 
 * <AUTHOR>
 * @since 2025-01-23
 */
public class TnmDataImporter {

    public static void main(String[] args) {
        // 启动Spring Boot应用上下文
        ConfigurableApplicationContext context = SpringApplication.run(YyhisServerApplication.class, args);
        
        try {
            // 获取服务实例
            SystemDictDataService systemDictDataService = context.getBean(SystemDictDataService.class);
            
            // 执行导入
            importTnmData(systemDictDataService);
            
            System.out.println("TNM数据导入完成！");
        } catch (Exception e) {
            System.err.println("导入失败: " + e.getMessage());
            e.printStackTrace();
        } finally {
            // 关闭应用上下文
            context.close();
        }
    }

    /**
     * 导入TNM数据
     */
    private static void importTnmData(SystemDictDataService systemDictDataService) throws IOException {
        ClassPathResource resource = new ClassPathResource("templates/临床TNM20250724.xlsx");
        
        try (InputStream inputStream = resource.getInputStream()) {
            List<SystemDictData> allDictDataList = new ArrayList<>();
            
            List<TnmRecord> tnmRecords = ExcelReader.readExcel(inputStream, row -> {
                // Excel列：癌种、指标分类、T值域、N值域、M值域、FIGO分期/CNLC分期
                if (row.size() < 6) {
                    return null;
                }

                String cancerType = row.get(0);
                String indicatorCategory = row.get(1);
                String tValues = row.get(2);
                String nValues = row.get(3);
                String mValues = row.get(4);
                String figoValues = row.get(5);

                // 检查必要字段
                if (cancerType == null || cancerType.trim().isEmpty()) {
                    return null;
                }

                TnmRecord record = new TnmRecord();
                record.cancerType = cancerType.trim();
                record.indicatorCategory = indicatorCategory;
                record.tValues = tValues;
                record.nValues = nValues;
                record.mValues = mValues;
                record.figoValues = figoValues;

                return record;
            });
            
            // 处理每条记录
            for (TnmRecord record : tnmRecords) {
                if (record == null) continue;

                // 处理T值域
                processDictValues(allDictDataList, record.cancerType, "T", record.tValues);

                // 处理N值域
                processDictValues(allDictDataList, record.cancerType, "N", record.nValues);

                // 处理M值域
                processDictValues(allDictDataList, record.cancerType, "M", record.mValues);

                // 处理FIGO分期/CNLC分期
                processDictValues(allDictDataList, record.cancerType, "FC", record.figoValues);
            }
            
            // 批量保存
            if (!allDictDataList.isEmpty()) {
                System.out.println("准备保存 " + allDictDataList.size() + " 条字典数据");
                systemDictDataService.saveBatch(allDictDataList);
                System.out.println("成功保存 " + allDictDataList.size() + " 条字典数据");
            } else {
                System.out.println("没有找到有效的数据");
            }
        }
    }

    /**
     * 处理字典值
     */
    private static void processDictValues(List<SystemDictData> dictDataList, String cancerType,
                                        String valueType, String values) {
        if (values == null || values.trim().isEmpty()) {
            return;
        }

        // 按逗号拆分值
        String[] valueArray = values.split(",");

        for (String value : valueArray) {
            value = value.trim();
            if (value.isEmpty()) {
                continue;
            }

            // 生成字典类型
            String dictType = generateDictType(cancerType, valueType);

            // 创建字典数据
            SystemDictData dictData = new SystemDictData();
            dictData.setDictType(dictType);
            dictData.setValue(value);
            dictData.setLabel(value);
            dictData.setStatus(0); // 正常状态
            dictData.setSort(0);
            dictData.setCreator("system");
            dictData.setCreateTime(LocalDateTime.now());

            dictDataList.add(dictData);
        }
    }

    /**
     * 生成字典类型
     * 规则：癌种 + "-" + 值类型
     */
    private static String generateDictType(String cancerType, String valueType) {
        String suffix;
        switch (valueType) {
            case "T":
                suffix = "T";
                break;
            case "N":
                suffix = "N";
                break;
            case "M":
                suffix = "M";
                break;
            case "FC":
                suffix = "FC";
                break;
            default:
                suffix = valueType;
        }
        return cancerType + "-" + suffix;
    }

    /**
     * TNM记录内部类
     */
    private static class TnmRecord {
        String cancerType;
        String indicatorCategory;
        String tValues;
        String nValues;
        String mValues;
        String figoValues;
    }
}
