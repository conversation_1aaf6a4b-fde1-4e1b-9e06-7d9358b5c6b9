
package cn.wbw.yyhis.util;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.openxml4j.util.ZipSecureFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.function.Function;

public class ExcelReader {

    static {
        // Lower the minimum inflation ratio to allow processing of the ICD10 file
        ZipSecureFile.setMinInflateRatio(0.001);
    }

    public static <T> List<T> readExcel(InputStream is, Function<List<String>, T> rowMapper) throws IOException {
        try (Workbook workbook = new XSSFWorkbook(is)) {
            Sheet sheet = workbook.getSheetAt(0);
            List<T> list = new ArrayList<>();
            // 跳过标题行
            Iterator<Row> rowIterator = sheet.iterator();
            if (rowIterator.hasNext()) {
                rowIterator.next();
            }
            while (rowIterator.hasNext()) {
                Row row = rowIterator.next();
                List<String> rowData = new ArrayList<>();
                // 根据Excel列数读取，防止读取过多空单元格
                int maxCellNum = row.getSheet().getRow(0).getLastCellNum();
                for (int i = 0; i < maxCellNum; i++) {
                    rowData.add(getCellValue(row.getCell(i)));
                }
                T mappedObject = rowMapper.apply(rowData);
                if (mappedObject != null) {
                    list.add(mappedObject);
                }
            }
            return list;
        }
    }

    private static String getCellValue(Cell cell) {
        if (cell == null) {
            return "";
        }
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue().trim();
            case NUMERIC:
                // 避免科学计数法
                return new java.math.BigDecimal(cell.getNumericCellValue()).toPlainString();
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                try {
                    return cell.getStringCellValue().trim();
                } catch (IllegalStateException e) {
                    try {
                        return new java.math.BigDecimal(cell.getNumericCellValue()).toPlainString();
                    } catch (IllegalStateException e2) {
                        return "";
                    }
                }
            default:
                return "";
        }
    }
} 