package cn.wbw.yyhis.mapper;

import cn.wbw.yyhis.model.dto.DepartmentPatientCountDTO;
import cn.wbw.yyhis.model.entity.B032;
import cn.wbw.yyhis.model.vo.PatientCardVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 住院患者信息表(在院) Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@Mapper
public interface B032Mapper extends BaseMapper<B032> {
    List<PatientCardVO> selectPatientCardsByDeptCode(@Param("departmentCode") String departmentCode, @Param("source") String source);

    List<DepartmentPatientCountDTO> countPatientsByDepartment(String source);

    PatientCardVO getPatientCard(String visitSn);
}