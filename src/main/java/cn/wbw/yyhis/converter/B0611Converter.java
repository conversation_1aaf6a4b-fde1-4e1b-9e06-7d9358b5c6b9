package cn.wbw.yyhis.converter;

import cn.wbw.yyhis.model.dto.B0611DTO;
import cn.wbw.yyhis.model.dto.B0611UpsertDTO;
import cn.wbw.yyhis.model.entity.B0611;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * B0611 Converter
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@Mapper
public interface B0611Converter {

    B0611Converter INSTANCE = Mappers.getMapper(B0611Converter.class);

    B0611 dtoToEntity(B0611DTO dto);

    B0611 dtoToEntity(B0611UpsertDTO dto);

    B0611DTO entityToDto(B0611 entity);

    List<B0611> dtoListToEntityList(List<B0611DTO> dtoList);

    List<B0611DTO> entityListToDtoList(List<B0611> entityList);
} 