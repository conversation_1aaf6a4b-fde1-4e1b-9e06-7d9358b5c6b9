package cn.wbw.yyhis.converter;

import cn.wbw.yyhis.model.dto.B051DTO;
import cn.wbw.yyhis.model.dto.B051UpsertDTO;
import cn.wbw.yyhis.model.entity.B051;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * B051 Converter
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@Mapper
public interface B051Converter {

    B051Converter INSTANCE = Mappers.getMapper(B051Converter.class);

    B051 dtoToEntity(B051DTO dto);

    B051 dtoToEntity(B051UpsertDTO dto);

    B051DTO entityToDto(B051 entity);

    List<B051> dtoListToEntityList(List<B051DTO> dtoList);

    List<B051DTO> entityListToDtoList(List<B051> entityList);
} 