package cn.wbw.yyhis.converter;

import cn.wbw.yyhis.model.dto.B0612DTO;
import cn.wbw.yyhis.model.dto.B0612UpsertDTO;
import cn.wbw.yyhis.model.entity.B0612;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * B0612 Converter
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@Mapper
public interface B0612Converter {

    B0612Converter INSTANCE = Mappers.getMapper(B0612Converter.class);

    B0612 dtoToEntity(B0612DTO dto);

    B0612 dtoToEntity(B0612UpsertDTO dto);

    B0612DTO entityToDto(B0612 entity);

    List<B0612> dtoListToEntityList(List<B0612DTO> dtoList);

    List<B0612DTO> entityListToDtoList(List<B0612> entityList);
} 