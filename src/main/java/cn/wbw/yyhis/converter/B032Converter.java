package cn.wbw.yyhis.converter;

import cn.wbw.yyhis.model.dto.B032DTO;
import cn.wbw.yyhis.model.entity.B032;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * B032 Converter
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@Mapper
public interface B032Converter {

    B032Converter INSTANCE = Mappers.getMapper(B032Converter.class);

    B032 dtoToEntity(B032DTO dto);

    B032DTO entityToDto(B032 entity);

    List<B032> dtoListToEntityList(List<B032DTO> dtoList);

    List<B032DTO> entityListToDtoList(List<B032> entityList);
} 