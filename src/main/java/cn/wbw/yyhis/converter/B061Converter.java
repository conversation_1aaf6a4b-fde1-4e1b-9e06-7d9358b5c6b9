package cn.wbw.yyhis.converter;

import cn.wbw.yyhis.model.dto.B061DTO;
import cn.wbw.yyhis.model.dto.B061UpsertDTO;
import cn.wbw.yyhis.model.entity.B061;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface B061Converter {

    B061Converter INSTANCE = Mappers.getMapper(B061Converter.class);

    B061DTO toDto(B061 b061);

    B061 toEntity(B061DTO b061DTO);

    B061 toEntity(B061UpsertDTO b061UpsertDTO);
}