package cn.wbw.yyhis.converter;

import cn.wbw.yyhis.model.dto.B021DTO;
import cn.wbw.yyhis.model.dto.B021UpsertDTO;
import cn.wbw.yyhis.model.entity.B021;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * B021 Converter
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@Mapper
public interface B021Converter {

    B021Converter INSTANCE = Mappers.getMapper(B021Converter.class);

    B021 dtoToEntity(B021DTO dto);

    B021 dtoToEntity(B021UpsertDTO dto);

    B021DTO entityToDto(B021 entity);

    List<B021> dtoListToEntityList(List<B021DTO> dtoList);

    List<B021DTO> entityListToDtoList(List<B021> entityList);
}
