package cn.wbw.yyhis.converter;

import cn.wbw.yyhis.model.entity.B162;
import cn.wbw.yyhis.model.dto.B162DTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <p>
 * 病理检查记录 转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-26
 */
@Mapper(componentModel = "spring")
public interface B162Converter {

    B162Converter INSTANCE = Mappers.getMapper(B162Converter.class);

    B162 dtoToEntity(B162DTO dto);

    B162DTO entityToDto(B162 entity);
} 