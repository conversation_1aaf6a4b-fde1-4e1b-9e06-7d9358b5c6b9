package cn.wbw.yyhis.converter;

import cn.wbw.yyhis.model.dto.B0613DTO;
import cn.wbw.yyhis.model.dto.B0613UpsertDTO;
import cn.wbw.yyhis.model.entity.B0613;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * B0613 Converter
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@Mapper
public interface B0613Converter {

    B0613Converter INSTANCE = Mappers.getMapper(B0613Converter.class);

    B0613 dtoToEntity(B0613DTO dto);

    B0613 dtoToEntity(B0613UpsertDTO dto);

    B0613DTO entityToDto(B0613 entity);

    List<B0613> dtoListToEntityList(List<B0613DTO> dtoList);

    List<B0613DTO> entityListToDtoList(List<B0613> entityList);
} 