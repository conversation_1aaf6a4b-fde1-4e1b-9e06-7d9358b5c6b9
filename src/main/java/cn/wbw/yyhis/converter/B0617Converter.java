package cn.wbw.yyhis.converter;

import cn.wbw.yyhis.model.dto.B0617DTO;
import cn.wbw.yyhis.model.dto.B0617UpsertDTO;
import cn.wbw.yyhis.model.entity.B0617;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface B0617Converter {

    B0617Converter INSTANCE = Mappers.getMapper(B0617Converter.class);

    B0617DTO toDto(B0617 b0617);

    B0617 toEntity(B0617DTO b0617DTO);

    B0617 toEntity(B0617UpsertDTO b0617UpsertDTO);
} 