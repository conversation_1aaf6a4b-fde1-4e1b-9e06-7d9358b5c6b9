package cn.wbw.yyhis.converter;

import cn.wbw.yyhis.model.dto.B071DTO;
import cn.wbw.yyhis.model.dto.B071UpsertDTO;
import cn.wbw.yyhis.model.entity.B071;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface B071Converter {

    B071Converter INSTANCE = Mappers.getMapper(B071Converter.class);

    B071DTO toDto(B071 b071);

    B071 toEntity(B071DTO b071DTO);

    B071 toEntity(B071UpsertDTO b071UpsertDTO);
} 