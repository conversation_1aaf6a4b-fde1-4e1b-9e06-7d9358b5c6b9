package cn.wbw.yyhis.converter;

import cn.wbw.yyhis.model.dto.B023DTO;
import cn.wbw.yyhis.model.dto.B023UpsertDTO;
import cn.wbw.yyhis.model.entity.B023;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * B023 Converter
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@Mapper
public interface B023Converter {

    B023Converter INSTANCE = Mappers.getMapper(B023Converter.class);

    B023 dtoToEntity(B023DTO dto);

    B023 dtoToEntity(B023UpsertDTO dto);

    B023DTO entityToDto(B023 entity);

    List<B023> dtoListToEntityList(List<B023DTO> dtoList);

    List<B023DTO> entityListToDtoList(List<B023> entityList);
} 