package cn.wbw.yyhis.converter;

import cn.wbw.yyhis.model.dto.TnmTStagingDTO;
import cn.wbw.yyhis.model.dto.TnmTStagingUpsertDTO;
import cn.wbw.yyhis.model.entity.TnmTStaging;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * TNM分期记录 Converter
 *
 * <AUTHOR>
 * @since 2024-07-07
 */
@Mapper
public interface TnmTStagingConverter {

    TnmTStagingConverter INSTANCE = Mappers.getMapper(TnmTStagingConverter.class);

    TnmTStaging dtoToEntity(TnmTStagingDTO dto);

    TnmTStaging dtoToEntity(TnmTStagingUpsertDTO dto);

    TnmTStagingDTO entityToDto(TnmTStaging entity);

    List<TnmTStaging> dtoListToEntityList(List<TnmTStagingDTO> dtoList);

    List<TnmTStagingDTO> entityListToDtoList(List<TnmTStaging> entityList);
}
