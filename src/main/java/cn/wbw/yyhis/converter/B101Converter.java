package cn.wbw.yyhis.converter;

import cn.wbw.yyhis.model.dto.B101DTO;
import cn.wbw.yyhis.model.dto.B101UpsertDTO;
import cn.wbw.yyhis.model.entity.B101;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface B101Converter {

    B101Converter INSTANCE = Mappers.getMapper(B101Converter.class);


    B101 toEntity(B101DTO b101DTO);

    B101 toEntity(B101UpsertDTO b101UpsertDTO);

    B101DTO entityToDto(B101 entity);

    List<B101DTO> entityListToDtoList(List<B101> entityList);
}