package cn.wbw.yyhis.converter;

import cn.wbw.yyhis.model.entity.B163;
import cn.wbw.yyhis.model.dto.B163DTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <p>
 * 分子病理检测记录 转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-26
 */
@Mapper(componentModel = "spring")
public interface B163Converter {

    B163Converter INSTANCE = Mappers.getMapper(B163Converter.class);

    B163 dtoToEntity(B163DTO dto);

    B163DTO entityToDto(B163 entity);
} 