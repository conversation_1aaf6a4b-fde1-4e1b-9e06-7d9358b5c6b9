package cn.wbw.yyhis.converter;

import cn.wbw.yyhis.model.dto.B0616DTO;
import cn.wbw.yyhis.model.dto.B0616UpsertDTO;
import cn.wbw.yyhis.model.entity.B0616;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface B0616Converter {

    B0616Converter INSTANCE = Mappers.getMapper(B0616Converter.class);

    B0616DTO toDto(B0616 b0616);

    B0616 toEntity(B0616DTO b0616DTO);

    B0616 toEntity(B0616UpsertDTO b0616UpsertDTO);
} 