package cn.wbw.yyhis.converter;

import cn.wbw.yyhis.model.dto.B0614DTO;
import cn.wbw.yyhis.model.dto.B0614UpsertDTO;
import cn.wbw.yyhis.model.entity.B0614;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface B0614Converter {

    B0614Converter INSTANCE = Mappers.getMapper(B0614Converter.class);

    B0614DTO toDto(B0614 b0614);

    B0614 toEntity(B0614DTO b0614DTO);

    B0614 toEntity(B0614UpsertDTO b0614UpsertDTO);
} 