package cn.wbw.yyhis.converter;

import cn.wbw.yyhis.model.dto.B0615DTO;
import cn.wbw.yyhis.model.dto.B0615UpsertDTO;
import cn.wbw.yyhis.model.entity.B0615;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface B0615Converter {

    B0615Converter INSTANCE = Mappers.getMapper(B0615Converter.class);

    B0615DTO toDto(B0615 b0615);

    B0615 toEntity(B0615DTO b0615DTO);

    B0615 toEntity(B0615UpsertDTO b0615UpsertDTO);
} 