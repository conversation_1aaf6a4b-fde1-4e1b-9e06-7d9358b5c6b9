package cn.wbw.yyhis.converter;

import cn.wbw.yyhis.model.entity.B161;
import cn.wbw.yyhis.model.dto.B161DTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <p>
 * 常规检查记录 转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-26
 */
@Mapper(componentModel = "spring")
public interface B161Converter {

    B161Converter INSTANCE = Mappers.getMapper(B161Converter.class);

    B161 dtoToEntity(B161DTO dto);

    B161DTO entityToDto(B161 entity);
} 