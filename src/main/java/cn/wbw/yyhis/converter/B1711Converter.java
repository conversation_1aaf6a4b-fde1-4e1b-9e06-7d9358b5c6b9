package cn.wbw.yyhis.converter;

import cn.wbw.yyhis.model.entity.B1711;
import cn.wbw.yyhis.model.dto.B1711DTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <p>
 * 常规检验记录（明细） 转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-26
 */
@Mapper(componentModel = "spring")
public interface B1711Converter {

    B1711Converter INSTANCE = Mappers.getMapper(B1711Converter.class);

    B1711 dtoToEntity(B1711DTO dto);

    B1711DTO entityToDto(B1711 entity);
} 