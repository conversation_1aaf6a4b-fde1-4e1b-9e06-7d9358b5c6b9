package cn.wbw.yyhis.converter;

import cn.wbw.yyhis.model.dto.SystemDictDataDTO;
import cn.wbw.yyhis.model.entity.SystemDictData;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * SystemDictData Converter
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@Mapper
public interface SystemDictDataConverter {

    SystemDictDataConverter INSTANCE = Mappers.getMapper(SystemDictDataConverter.class);

    SystemDictData dtoToEntity(SystemDictDataDTO dto);

    SystemDictDataDTO entityToDto(SystemDictData entity);

    List<SystemDictData> dtoListToEntityList(List<SystemDictDataDTO> dtoList);

    List<SystemDictDataDTO> entityListToDtoList(List<SystemDictData> entityList);
} 