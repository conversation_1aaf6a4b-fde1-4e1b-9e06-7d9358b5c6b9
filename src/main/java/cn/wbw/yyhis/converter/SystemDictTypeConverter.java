package cn.wbw.yyhis.converter;

import cn.wbw.yyhis.model.dto.SystemDictTypeDTO;
import cn.wbw.yyhis.model.entity.SystemDictType;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * SystemDictType Converter
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@Mapper
public interface SystemDictTypeConverter {

    SystemDictTypeConverter INSTANCE = Mappers.getMapper(SystemDictTypeConverter.class);

    SystemDictType dtoToEntity(SystemDictTypeDTO dto);

    SystemDictTypeDTO entityToDto(SystemDictType entity);

    List<SystemDictType> dtoListToEntityList(List<SystemDictTypeDTO> dtoList);

    List<SystemDictTypeDTO> entityListToDtoList(List<SystemDictType> entityList);
} 