package cn.wbw.yyhis.filter;

import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletRequestWrapper;
import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

@Slf4j
public class UrlDecodeFilter implements Filter {

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        
        // 创建包装后的请求对象
        HttpServletRequestWrapper wrappedRequest = new HttpServletRequestWrapper(httpRequest) {
            
            @Override
            public String getParameter(String name) {
                String value = super.getParameter(name);
                return decodeUrl(value);
            }
            
            @Override
            public String[] getParameterValues(String name) {
                String[] values = super.getParameterValues(name);
                if (values == null) {
                    return null;
                }
                String[] decodedValues = new String[values.length];
                for (int i = 0; i < values.length; i++) {
                    decodedValues[i] = decodeUrl(values[i]);
                }
                return decodedValues;
            }
            
            @Override
            public Map<String, String[]> getParameterMap() {
                Map<String, String[]> originalMap = super.getParameterMap();
                Map<String, String[]> decodedMap = new HashMap<>();
                
                for (Map.Entry<String, String[]> entry : originalMap.entrySet()) {
                    String[] values = entry.getValue();
                    String[] decodedValues = new String[values.length];
                    for (int i = 0; i < values.length; i++) {
                        decodedValues[i] = decodeUrl(values[i]);
                    }
                    decodedMap.put(entry.getKey(), decodedValues);
                }
                
                return decodedMap;
            }
            
            @Override
            public String getRequestURI() {
                String uri = super.getRequestURI();
                return decodeUrl(uri);
            }
            
            @Override
            public String getPathInfo() {
                String pathInfo = super.getPathInfo();
                return decodeUrl(pathInfo);
            }
            
            @Override
            public String getPathTranslated() {
                String pathTranslated = super.getPathTranslated();
                return decodeUrl(pathTranslated);
            }
            
            @Override
            public String getQueryString() {
                String queryString = super.getQueryString();
                return decodeUrl(queryString);
            }
            
            private String decodeUrl(String value) {
                if (value == null) {
                    return null;
                }
                try {
                    return URLDecoder.decode(value, StandardCharsets.UTF_8.name());
                } catch (UnsupportedEncodingException e) {
                    log.error("URL解码失败: {}", value, e);
                    return value;
                }
            }
        };
        
        chain.doFilter(wrappedRequest, response);
    }
    
    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        // 初始化逻辑
    }
    
    @Override
    public void destroy() {
        // 销毁逻辑
    }
}