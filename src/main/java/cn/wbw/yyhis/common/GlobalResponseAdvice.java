package cn.wbw.yyhis.common;

import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

@RestControllerAdvice(basePackages = "cn.wbw.yyhis.controller")
public class GlobalResponseAdvice implements ResponseBodyAdvice<Object> {

    @Override
    public boolean supports(MethodParameter returnType, Class<? extends HttpMessageConverter<?>> converterType) {
        // 如果接口返回的类型本身就是ApiResult，则无需重复包装
        return !returnType.getParameterType().equals(ApiResult.class);
    }

    @Override
    public Object beforeBodyWrite(Object body, MethodParameter returnType, MediaType selectedContentType,
                                  Class<? extends HttpMessageConverter<?>> selectedConverterType,
                                  ServerHttpRequest request, ServerHttpResponse response) {
        if (body instanceof ApiResult) {
            return body;
        }
        // String类型不能直接包装，需特殊处理
        if (returnType.getGenericParameterType().equals(String.class)) {
            // 这里可以考虑返回JSON字符串，但为了简单起见，我们直接返回成功对象，让Spring处理序列化
            // 注意：如果直接返回String，Spring会使用StringHttpMessageConverter，可能导致类型转换错误
            // 为保持一致性，所有数据都应封装在ApiResult中
             return ApiResult.success(body);
        }
        // 对于其他所有类型，直接封装
        return ApiResult.success(body);
    }
} 