package cn.wbw.yyhis.common;

import lombok.Data;

import java.io.Serializable;

/**
 * 通用返回
 *
 * @param <T>
 */
@Data
public class ApiResult<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 成功
     */
    public static final int CODE_SUCCESS = 0;
    /**
     * 失败
     */
    public static final int CODE_ERROR = 500;

    private int code;
    private String msg;
    private T data;

    public static <T> ApiResult<T> success() {
        return success(null);
    }

    public static <T> ApiResult<T> success(T data) {
        ApiResult<T> result = new ApiResult<>();
        result.setCode(CODE_SUCCESS);
        result.setMsg("操作成功");
        result.setData(data);
        return result;
    }

    public static <T> ApiResult<T> error() {
        return error("操作失败");
    }

    public static <T> ApiResult<T> error(String msg) {
        ApiResult<T> result = new ApiResult<>();
        result.setCode(CODE_ERROR);
        result.setMsg(msg);
        return result;
    }

    public static <T> ApiResult<T> error(T data, String msg) {
        ApiResult<T> result = new ApiResult<>();
        result.setCode(CODE_ERROR);
        result.setMsg(msg);
        result.setData(data);
        return result;
    }

    public static <T> ApiResult<T> error(int code, String msg) {
        ApiResult<T> result = new ApiResult<>();
        result.setCode(code);
        result.setMsg(msg);
        return result;
    }
    public static <T> ApiResult<T> error(int code, T data, String msg) {
        ApiResult<T> result = new ApiResult<>();
        result.setCode(code);
        result.setMsg(msg);
        result.setData(data);
        return result;
    }
}