package cn.wbw.yyhis.model.dto;

import lombok.Data;

/**
 * <p>
 * 患者就诊基本信息表 新增/更新 DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@Data
public class B021UpsertDTO {

    /**
     * ABO血型
     */
    private String aboBloodType;

    /**
     * 入院时间
     */
    private String admissionDatetime;

    /**
     * 血型首位代码
     */
    private String bloodTypeS;

    /**
     * 血型末位代码
     */
    private String bolldTypeE;

    /**
     * 证件号码
     */
    private String certificateNo;

    /**
     * 证件类型
     */
    private String certificateType;

    /**
     * 紧急联系人1
     */
    private String contactPerson1;

    /**
     * 紧急联系人2
     */
    private String contactPerson2;

    /**
     * 紧急联系人电话1
     */
    private String contactPhoneNo1;

    /**
     * 紧急联系人电话2
     */
    private String contactPhoneNo2;

    /**
     * 出生日期
     */
    private String dateOfBirth;

    /**
     * 出院时间
     */
    private String dischargeDatetime;

    /**
     * 户籍地址-详细地址
     */
    private String domicileAddress;

    /**
     * 户籍地址-市名称
     */
    private String domicileCity;

    /**
     * 户籍地址-县名称
     */
    private String domicileCounty;

    /**
     * 户籍地址-省名称
     */
    private String domicileProvince;

    /**
     * 文化程度
     */
    private String education;

    /**
     * 文化程度代码
     */
    private String educationCode;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 民族
     */
    private String ethnicity;

    /**
     * 扩展字段1
     */
    private String extendData1;

    /**
     * 扩展字段2
     */
    private String extendData2;

    /**
     * 性别
     */
    private String gender;

    /**
     * 健康卡号
     */
    private String healthCardNo;

    /**
     * 健康卡类型
     */
    private String healthCardType;

    /**
     * 身高
     */
    private String height;

    /**
     * 现住址
     */
    private String homeAddress;

    /**
     * 组织机构代码
     */
    private String hospitalCode;

    /**
     * 医疗机构名称
     */
    private String hospitalName;

    /**
     * 住院次数
     */
    private String hospitalizationTimes;

    /**
     * 身份证号码
     */
    private String idcardNo;

    /**
     * 住院号
     */
    private String inpatientNo;

    /**
     * 医保卡号
     */
    private String insuranceNo;

    /**
     * 费用类型
     */
    private String insuranceType;

    /**
     * 是否院内感染
     */
    private String isHospitalInfected;

    /**
     * 婚姻状况类别名称
     */
    private String maritalStatus;

    /**
     * 婚姻状况类别代码
     */
    private String maritalStatusCode;

    /**
     * 病案号
     */
    private String medicalRecordNo;

    /**
     * 患者姓名
     */
    private String name;

    /**
     * 国籍
     */
    private String nationality;

    /**
     * 是否新生儿
     */
    private String newbronMark;

    /**
     * 职业类别代码
     */
    private String occupationCode;

    /**
     * 职业类别名称
     */
    private String occupationName;

    /**
     * 门诊号
     */
    private String outpatientNo;

    /**
     * 性别编码
     */
    private String patientGender;

    /**
     * 患者ID
     */
    private String patientId;

    /**
     * 患者原始ID
     */
    private String patientIdOld;

    /**
     * 患者身份
     */
    private String patientIdentity;

    /**
     * 本人电话号码
     */
    private String phoneNo;

    /**
     * 联系电话2
     */
    private String phoneNo2;

    /**
     * 记录创建时间
     */
    private String recordDatetime;

    /**
     * 记录状态
     */
    private Integer recordStatus;

    /**
     * Rh血型
     */
    private String rhBloodType;

    /**
     * 特殊病例标识
     */
    private String tsblbs;

    /**
     * 就诊卡号
     */
    private String visitCardNo;

    /**
     * 就诊时间
     */
    private String visitDatetime;

    /**
     * 就诊医生姓名
     */
    private String visitDoctorName;

    /**
     * 就诊医生代码
     */
    private String visitDoctorNo;

    /**
     * 单次就诊唯一标识号
     */
    private String visitSn;

    /**
     * 是否在院
     */
    private String visitStatus;

    /**
     * 就诊次数
     */
    private String visitTimes;

    /**
     * 就诊类型
     */
    private String visitType;

    /**
     * 体重
     */
    private String weight;

    /**
     * 微信
     */
    private String weixin;

    /**
     * 分院编码
     */
    private String branchCode;

    /**
     * 分院名称
     */
    private String branchName;
}
