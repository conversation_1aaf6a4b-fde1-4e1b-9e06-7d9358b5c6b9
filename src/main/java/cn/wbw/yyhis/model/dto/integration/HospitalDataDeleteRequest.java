package cn.wbw.yyhis.model.dto.integration;

import cn.wbw.yyhis.config.SnakeCaseListSerializer;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import java.util.List;

/**
 * 符合第三方接口文档要求的删除请求格式
 * 删除接口相比数据推送接口，请求体结构更简单，主要包含联合主键信息
 */
@Data
public class HospitalDataDeleteRequest {
    
    // 患者基本信息（必填）
    private String patientId;
    private String visitSn;
    private String doctorId;
    private String deptId;
    
    // 数据包（包含要删除的数据）
    private List<DeleteDataPacket> dataPacket;
    
    // 扩展字段
    private String extend1;
    private String extend2;
    private String extend3;
    private String extend4;
    private String extend5;
    private String extend6;
    private String extend7;
    private String extend8;
    
    @Data
    public static class DeleteDataPacket {
        /**
         * 表代码，指定要删除数据的表
         */
        private String tableCode;
        
        /**
         * 要删除的数据，包含联合主键字段
         * 根据文档说明：传入字段为当前表根据业务定义的联合主键
         */
        @JsonSerialize(using = SnakeCaseListSerializer.class)
        private List<?> data;
    }
}
