package cn.wbw.yyhis.model.dto;

import lombok.Data;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 用于B0615(有创操作记录)的添加和更新操作
 */
@Data
public class B0615UpsertDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 病程记录流水号 (更新时使用)
     */
    private String recordSn;

    /**
     * 单次就诊唯一标识号
     */
    private String visitSn;

    /**
     * 操作日期
     */
    private LocalDateTime perfromDatetime;

    /**
     * 操作名称
     */
    private String procedureName;

    /**
     * 操作记录
     */
    private String recordContent;

    /**
     * 注意事项
     */
    private String mattersNeedingAttention;

}
