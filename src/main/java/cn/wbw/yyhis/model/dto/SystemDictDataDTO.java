package cn.wbw.yyhis.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * SystemDictData DTO
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@Data
public class SystemDictDataDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    private Integer sort;
    private String label;
    private String value;
    private String dictType;
    private Integer status;
    private String colorType;
    private String cssClass;
    private String remark;
    private String creator;
    private LocalDateTime createTime;
    private String updater;
    private LocalDateTime updateTime;
    private Boolean deleted;
} 