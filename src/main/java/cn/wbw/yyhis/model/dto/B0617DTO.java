package cn.wbw.yyhis.model.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 术后首次病程 DTO
 */
@Data
public class B0617DTO {

    private String anesthesiaMethod;
    private String contentType;
    private String extendData1;
    private String extendData2;
    private String fromTable;
    private String fromYyRecordId;
    private String hospitalCode;
    private String hospitalName;
    private String hospitalizationTimes;
    private String inpatientNo;
    private String mattersNeedCaution;
    private String medicalNoteDate;
    private String medicalRecordNo;
    private String patientId;
    private String patientIdOld;
    private String postOperationDiagnosis;
    private String postoperationTreatment;
    private LocalDateTime recordDatetime;
    private String recordSn;
    private Integer recordStatus;
    private String recordTemplateName;
    private String recordText;
    private String recordTitle;
    private String recordTitleCode;
    private LocalDateTime recordUpdateDatetime;
    private String signatureDoctor;
    private LocalDateTime surgeryEndDatetime;
    private String surgeryMethod;
    private String surgeryName;
    private String surgeryProcess;
    private LocalDateTime surgeryStartDatetime;
    private String visitSn;
    private LocalDateTime yyCollectionDatetime;
    private Long yyRecordId;
    private String yyRecordMd5;
    private Integer yyUploadStatus;
    private LocalDateTime yyEtlTime;
    private LocalDateTime yyUploadTime;
    private String yyBatchTime;
    private String yyRecordBatchId;
    private LocalDateTime yyBackfillTime;
    private Integer yyBackfillStatus;
    private String branchCode;
    private String branchName;
    private LocalDateTime dateForPartition;
} 