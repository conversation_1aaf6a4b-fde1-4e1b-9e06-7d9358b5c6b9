package cn.wbw.yyhis.model.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 手术记录 DTO
 */
@Data
public class B0616DTO {

    private String anesthesiaDoctorCode;
    private String anesthesiaDoctorName;
    private LocalDateTime anesthesiaEndtime;
    private String anesthesiaMethod;
    private LocalDateTime anesthesiaStarttime;
    private String bleedingVolum;
    private String contentType;
    private String extendData1;
    private String extendData2;
    private String fromTable;
    private String fromYyRecordId;
    private String hospitalCode;
    private String hospitalName;
    private String hospitalizationTimes;
    private String inpatientNo;
    private String medicalNoteDate;
    private String medicalRecordNo;
    private String operationTreatment;
    private String patientId;
    private String patientIdOld;
    private String postOperationDiagnosis;
    private String preOperativeDiagnosis;
    private LocalDateTime recordDatetime;
    private String recordSn;
    private Integer recordStatus;
    private String recordTemplateName;
    private String recordText;
    private String recordTitle;
    private String recordTitleCode;
    private LocalDateTime recordUpdateDatetime;
    private String signatureDoctor;
    private String surgeryDoctorCode;
    private String surgeryDoctorName;
    private LocalDateTime surgeryEndtime;
    private String surgeryName;
    private String surgeryProcess;
    private LocalDateTime surgeryStarttime;
    private LocalDateTime surgeryTime;
    private String visitSn;
    private String volumeBlood;
    private LocalDateTime yyCollectionDatetime;
    private Long yyRecordId;
    private String yyRecordMd5;
    private Integer yyUploadStatus;
    private LocalDateTime yyEtlTime;
    private LocalDateTime yyUploadTime;
    private String yyBatchTime;
    private String yyRecordBatchId;
    private LocalDateTime yyBackfillTime;
    private Integer yyBackfillStatus;
    private String branchCode;
    private String branchName;
    private LocalDateTime dateForPartition;
} 