package cn.wbw.yyhis.model.dto;

import lombok.Data;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 常规检查记录 DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-26
 */
@Data
public class B161DTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String visitSn;
    private String reportNo;
    private String examItemType;
    private String examSites;
    private String recordDatetime;
    private String examDatetime;
    private String applyDatetime;
    private String examItemName;
    private String examDiagConclusion;
    private String examDiagDescription;

}