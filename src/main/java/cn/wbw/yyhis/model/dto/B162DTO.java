package cn.wbw.yyhis.model.dto;

import lombok.Data;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 病理检查记录 DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-26
 */
@Data
public class B162DTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String reportNo;
    private String pathologyTestType;
    private String reportDatetime;
    private String applyDatetime;
    private String grossDescription;
    private String pathoDiagConclusion;
}