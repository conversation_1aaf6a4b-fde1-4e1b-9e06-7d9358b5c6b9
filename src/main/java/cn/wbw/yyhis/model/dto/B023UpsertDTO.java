package cn.wbw.yyhis.model.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 用于B023(患者诊断记录)的添加和更新操作
 */
@Data
public class B023UpsertDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 诊断ID号 (更新时使用)
     */
    private String diagId;

    /**
     * 诊断数据来源 (更新时使用)
     */
    private String diagSource;

    /**
     * 单次就诊唯一标识号
     */
    private String visitSn;

    /**
     * 诊断名称
     */
    private String diagName;

    /**
     * 诊断编码
     */
    private String diagCode;

    /**
     * 诊断类型
     */
    private String diagType;

    /**
     * 是否主要诊断
     */
    private String maindiagMark;

    /**
     * 诊断时间
     */
    private String diagDatetime;

    /**
     * 诊断说明
     */
    private String diagExplanation;
} 