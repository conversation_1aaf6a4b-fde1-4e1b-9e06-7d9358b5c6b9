package cn.wbw.yyhis.model.dto.integration;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

public class DataPacket<T> {

    @JsonProperty("czlx")
    private int operationType; // 1-新增，2-更新，3-删除

    private List<T> data;

    // Getters and Setters
    public int getOperationType() {
        return operationType;
    }

    public void setOperationType(int operationType) {
        this.operationType = operationType;
    }

    public List<T> getData() {
        return data;
    }

    public void setData(List<T> data) {
        this.data = data;
    }
} 