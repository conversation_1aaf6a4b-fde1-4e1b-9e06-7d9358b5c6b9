package cn.wbw.yyhis.model.dto.integration;

import cn.wbw.yyhis.config.SnakeCaseListSerializer;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import java.util.List;

/**
 * 符合第三方接口文档要求的请求格式
 */
@Data
public class HospitalDataRequest {
    
    // 患者基本信息
    private String patientId;
    private String visitSn;
    private String doctorId;
    private String deptId;
    private String admissionDatetime;
    private String dischargeDatetime;
    private Integer inHospitalStatus;
    private String visit_type;
    private Integer visit_times;
    private String visit_datetime;
    private Integer hospitalization_times;
    private String current_dept_code;
    private String current_dept_name;
    private String visit_doctor_no;
    private String visit_doctor_name;
    private String qualityTarget;
    
    // 患者详细信息
    private PatientInfo patientInfo;
    
    // 数据包
    private List<HospitalDataPacket> dataPacket;
    
    // 扩展字段
    private String extend1;
    private String extend2;
    private String extend3;
    private String extend4;
    private String extend5;
    private String extend6;
    private String extend7;
    private String extend8;
    
    @Data
    public static class PatientInfo {
        private String name;
        private String gender;
        private String patient_gender;
        private String newbron_mark;
        private String birthDate;
        private Integer maritalStatus;
        private Integer pregnancyStatus;
    }
    

}
