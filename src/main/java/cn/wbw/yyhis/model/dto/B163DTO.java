package cn.wbw.yyhis.model.dto;

import lombok.Data;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 分子病理检测记录 DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-26
 */
@Data
public class B163DTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String reportNo;
    private String reportDatetime;
    private String applyDatetime;
    private String testMethod;
    private String testResult;

}