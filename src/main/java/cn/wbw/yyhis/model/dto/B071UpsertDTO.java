package cn.wbw.yyhis.model.dto;

import lombok.Data;
import java.io.Serializable;

/**
 * 用于B071(出院记录)的添加和更新操作
 */
@Data
public class B071UpsertDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 病程记录流水号 (更新时使用)
     */
    private String recordSn;

    /**
     * 单次就诊唯一标识号
     */
    private String visitSn;

    /**
     * 入院诊断
     */
    private String admissionDiag;

    /**
     * 出院诊断
     */
    private String dischargeDiag;


    /**
     * 入院情况
     */
    private String admissionCondition;

    /**
     * 诊疗经过
     */
    private String treatmentInfo;

    /**
     * 出院情况
     */
    private String dischargeCondition;

    /**
     * 出院医嘱
     */
    private String dischargeOrder;
} 