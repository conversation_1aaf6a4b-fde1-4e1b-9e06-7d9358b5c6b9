package cn.wbw.yyhis.model.dto;

import lombok.Data;
import java.io.Serializable;

/**
 * 用于B061(入院病程记录)的添加和更新操作
 */
@Data
public class B061UpsertDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 病程记录流水号 (更新时使用)
     */
    private String recordSn;

    /**
     * 单次就诊唯一标识号
     */
    private String visitSn;

    /**
     * 记录内容
     */
    private String recordContent;

}
