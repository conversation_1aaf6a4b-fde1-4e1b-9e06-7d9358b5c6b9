package cn.wbw.yyhis.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * B023 DTO
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@Data
public class B023DTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String confirmedDiagMark;
    private String createDiagnoseDeptCode;
    private String createDiagnoseDeptName;
    private String diagCode;
    private String diagDatetime;
    private String diagDoctorName;
    private String diagDoctorNo;
    private String diagExplanation;
    private String diagId;
    private String diagName;
    private String diagSerialNumber;
    private String diagSource;
    private String diagStatus;
    private String diagType;
    private String extendData1;
    private String extendData2;
    private String fromTable;
    private String fromYyRecordId;
    private String hospitalCode;
    private String hospitalName;
    private String hospitalizationTimes;
    private String inpatientNo;
    private String isComplication;
    private String maindiagMark;
    private String medicalRecordNo;
    private String name;
    private String outpatientNo;
    private String patientId;
    private String patientIdOld;
    private String recordDatetime;
    private Integer recordStatus;
    private LocalDateTime recordUpdateDatetime;
    private String tcmDiseaseCode;
    private String tcmDiseaseName;
    private String tcmSymptomCode;
    private String tcmSymptomName;
    private String visitCardNo;
    private String visitSn;
    private String visitTimes;
    private String visitType;
    private LocalDateTime yyCollectionDatetime;
    private Long yyRecordId;
    private String yyRecordMd5;
    private Integer yyUploadStatus;
    private LocalDateTime yyEtlTime;
    private LocalDateTime yyUploadTime;
    private String yyBatchTime;
    private String yyRecordBatchId;
    private LocalDateTime yyBackfillTime;
    private Integer yyBackfillStatus;
    private String branchCode;
    private String branchName;
    private LocalDateTime dateForPartition;
} 