package cn.wbw.yyhis.model.dto;

import lombok.Data;
import java.io.Serializable;

/**
 * 用于B0613(术前讨论)的添加和更新操作
 */
@Data
public class B0613UpsertDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 病程记录流水号 (更新时使用)
     */
    private String recordSn;

    /**
     * 单次就诊唯一标识号
     */
    private String visitSn;

    /**
     * 讨论日期
     */
    private String discussionDatetime;

    /**
     * 参加人员
     */
    private String participantsList;

    /**
     * 术前准备
     */
    private String preoperationPreparation;

    /**
     * 手术指征
     */
    private String operationIndication;

    /**
     * 手术方式
     */
    private String surgeryMethod;

    /**
     * 手术体位
     */
    private String surgeryPositions;

    /**
     * 手术步骤
     */
    private String operationSetps;

    /**
     * 术中注意事项
     */
    private String possibleProblemsAndSolution;

    /**
     * 讨论意见
     */
    private String discussionOpinion;

    /**
     * 讨论小结
     */
    private String hostConclusion;
}
