package cn.wbw.yyhis.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * SystemDictType DTO
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@Data
public class SystemDictTypeDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    private String name;
    private String type;
    private Integer status;
    private String remark;
    private String creator;
    private LocalDateTime createTime;
    private String updater;
    private LocalDateTime updateTime;
    private Boolean deleted;
    private LocalDateTime deletedTime;
} 