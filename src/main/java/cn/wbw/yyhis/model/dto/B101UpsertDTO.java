package cn.wbw.yyhis.model.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 用于B101(住院医嘱记录)的添加和更新操作
 */
@Data
public class B101UpsertDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 医嘱流水号 (更新时使用)
     */
    private String orderSn;

    /**
     * 单次就诊唯一标识号
     */
    private String visitSn;

    /**
     * 医嘱类型
     */
    private String orderType;

    /**
     * 医嘱类别代码
     */
    private String orderClassCode;

    /**
     * 医嘱类别名称
     */
    private String orderClassName;

    /**
     * 医嘱名称
     */
    private String orderItemName;

    /**
     * 药品规格
     */
    private String spec;

    /**
     * 频次代码
     */
    private String frequencyCode;

    /**
     * 频次名称
     */
    private String frequencyName;

    /**
     * 用法(给药途径)
     */
    private String administrationRoute;

    /**
     * 用量
     */
    private String dose;

    /**
     * 单位
     */
    private String doseUnit;

    /**
     * 开始时间
     */
    private String orderStartDatetime;

    /**
     * 结束时间
     */
    private String orderEndDatetime;
}
