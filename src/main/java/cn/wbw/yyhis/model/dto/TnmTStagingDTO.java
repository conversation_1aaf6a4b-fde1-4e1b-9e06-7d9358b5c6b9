package cn.wbw.yyhis.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * TNM分期记录 DTO
 *
 * <AUTHOR>
 * @since 2024-07-07
 */
@Data
public class TnmTStagingDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 临床诊断
     */
    private String diagnosisItemCode;

    /**
     * 肿瘤分期-T
     */
    private String tnmTStaging;

    /**
     * 肿瘤分期-N
     */
    private String tnmNStaging;

    /**
     * 肿瘤分期-M
     */
    private String tnmMStaging;

    /**
     * FIGO分期-宫颈癌
     */
    private String figoStagingCervicalCancer;

    /**
     * CNLC分期-肝细胞癌
     */
    private String cnlcStagingHepatocellular;

    /**
     * Ann Arbor分期-淋巴瘤
     */
    private String annArborStagingLymphoma;

    /**
     * 分期时间
     */
    private LocalDateTime stagingDatetime;

    /**
     * 记录创建时间
     */
    private LocalDateTime recordDatetime;

    /**
     * TNM分期记录流水号
     */
    private String recordSn;

    /**
     * 记录更新时间
     */
    private LocalDateTime recordUpdateDatetime;

    /**
     * 单次就诊唯一标识号
     */
    private String visitSn;
}
