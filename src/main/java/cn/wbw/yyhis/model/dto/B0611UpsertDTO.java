package cn.wbw.yyhis.model.dto;

import lombok.Data;
import java.io.Serializable;

/**
 * 用于B0611(首次病程记录)的添加和更新操作
 */
@Data
public class B0611UpsertDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 病程记录流水号 (更新时使用)
     */
    private String recordSn;

    /**
     * 单次就诊唯一标识号
     */
    private String visitSn;

    /**
     * 入院初步诊断
     */
    private String primaryDiagnosis;

    /**
     * 病历特点
     */
    private String caseCharacter;

    /**
     * 主诉
     */
    private String chiefComplaint;

    /**
     * 体格检查
     */
    private String physicalExam;

    /**
     * 辅助检查
     */
    private String auxiliaryExam;

    /**
     * 诊断依据
     */
    private String diagnosisBasis;

    /**
     * 鉴别诊断信息
     */
    private String differentiatedDiagnosisDesc;

    /**
     * 诊疗计划
     */
    private String treatmentPlan;
} 