package cn.wbw.yyhis.model.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 住院医嘱记录 DTO
 */
@Data
public class B101DTO {

    private String administrationRoute;
    private String antibioticsLevel;
    private String dose;
    private String doseUnit;
    private String drugCompatibility;
    private String drugFlag;
    private String drugOrder;
    private String executiveDeptCode;
    private String executiveDeptName;
    private String extendData1;
    private String extendData2;
    private String form;
    private String frequencyCode;
    private String frequencyName;
    private String fromTable;
    private String fromYyRecordId;
    private String herbalNote;
    private String hospitalCode;
    private String hospitalName;
    private String hospitalizationTimes;
    private String infusionDuration;
    private String injectionType;
    private String inpatientNo;
    private String itemTotalPrice;
    private String itemUnitPrice;
    private String jobTitle;
    private String liquidConfiguration;
    private String manufac;
    private String medicalRecordNo;
    private String numOfOrder;
    private String numOfOrderUnit;
    private String operationId;
    private String orderClassCode;
    private String orderClassName;
    private String orderConfirmTime;
    private String orderDeptCode;
    private String orderDeptName;
    private String orderDoctorName;
    private String orderDoctorNo;
    private String orderEndDatetime;
    private String orderGivenTime;
    private String orderGroupNo;
    private String orderItemCode;
    private String orderItemName;
    private String orderNote;
    private String orderSn;
    private String orderStartDatetime;
    private String orderStatus;
    private String orderType;
    private String patientId;
    private String patientIdOld;
    private String recordDatetime;
    private Integer recordStatus;
    private LocalDateTime recordUpdateDatetime;
    private String requestNo;
    private String spec;
    private String specialOrderFlag;
    private String technicalTitle;
    private String tradeName;
    private String visitDeptCode;
    private String visitDeptName;
    private String visitSn;
    private LocalDateTime yyCollectionDatetime;
    private Long yyRecordId;
    private String yyRecordMd5;
    private Integer yyUploadStatus;
    private LocalDateTime yyEtlTime;
    private LocalDateTime yyUploadTime;
    private String yyBatchTime;
    private String yyRecordBatchId;
    private LocalDateTime yyBackfillTime;
    private Integer yyBackfillStatus;
    private String branchCode;
    private String branchName;
    private LocalDateTime dateForPartition;
} 