package cn.wbw.yyhis.model.dto;

import lombok.Data;
import java.io.Serializable;

/**
 * 用于B051(入院记录)的添加和更新操作
 */
@Data
public class B051UpsertDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 入院记录流水号 (更新时使用)
     */
    private String recordSn;

    /**
     * 单次就诊唯一标识号
     */
    private String visitSn;

    /**
     * 主诉
     */
    private String chiefComplaint;

    /**
     * 现病史
     */
    private String currentMedhistory;

    /**
     * 既往史
     */
    private String pastMedhistory;

    /**
     * 个人史
     */
    private String personalMedhistory;

    /**
     * 月经史
     */
    private String menstrualHistory;

    /**
     * 婚育史
     */
    private String marriageBirthHistory;

    /**
     * 家族史
     */
    private String familyHistory;

    /**
     * 辅助检查
     */
    private String auxiliaryExam;

    /**
     * 体格检查
     */
    private String physicalExam;

    /**
     * 初步诊断
     */
    private String primaryDiagnosis;
} 