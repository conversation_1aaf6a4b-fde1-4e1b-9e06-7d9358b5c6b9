package cn.wbw.yyhis.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * B0612 DTO
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@Data
public class B0612DTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String auxiliaryExam;
    private String consApplyTime;
    private String consReason;
    private String consRecommendation;
    private String consTime;
    private String contentType;
    private String deptInvited;
    private String diagnosisConsultationCorrected;
    private String extendData1;
    private String extendData2;
    private String fromTable;
    private String fromYyRecordId;
    private String hospitalCode;
    private String hospitalName;
    private String hospitalizationTimes;
    private String inpatientNo;
    private String medicalNoteDate;
    private String medicalRecordNo;
    private String patientId;
    private String patientIdOld;
    private String recordAbstract;
    private LocalDateTime recordDatetime;
    private String recordSn;
    private Integer recordStatus;
    private String recordTemplateName;
    private String recordText;
    private String recordTitle;
    private String recordTitleCode;
    private LocalDateTime recordUpdateDatetime;
    private String signatureDoctor;
    private String treatProDescription;
    private String visitSn;
    private LocalDateTime yyCollectionDatetime;
    private Long yyRecordId;
    private String yyRecordMd5;
    private Integer yyUploadStatus;
    private LocalDateTime yyEtlTime;
    private LocalDateTime yyUploadTime;
    private String yyBatchTime;
    private String yyRecordBatchId;
    private LocalDateTime yyBackfillTime;
    private Integer yyBackfillStatus;
    private String branchCode;
    private String branchName;
    private LocalDateTime dateForPartition;
} 