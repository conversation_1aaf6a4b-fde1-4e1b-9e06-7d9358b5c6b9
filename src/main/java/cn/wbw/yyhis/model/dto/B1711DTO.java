package cn.wbw.yyhis.model.dto;

import lombok.Data;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 常规检验记录（明细） DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-26
 */
@Data
public class B1711DTO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 检验明细
     */
    private String itemName;
    /**
     * 结果
     */
    private String resultHybrid;
    private String resultQualitative;
    private String resultQuantitative;
    private String resultUnit;
    /**
     * 参考值
     */
    private String referenceRangeHigh;
    private String referenceRangeLow;

} 