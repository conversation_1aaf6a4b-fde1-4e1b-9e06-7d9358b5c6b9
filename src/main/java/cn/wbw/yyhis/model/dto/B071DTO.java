package cn.wbw.yyhis.model.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 出院记录 DTO
 */
@Data
public class B071DTO {

    private String admissionCondition;
    private String admissionDatetime;
    private String admissionDiag;
    private String contentType;
    private String dischargeCondition;
    private String dischargeDatetime;
    private String dischargeDiag;
    private String dischargeOrder;
    private String dischargeReason;
    private String ecogScore;
    private String extendData1;
    private String extendData2;
    private String fromTable;
    private String fromYyRecordId;
    private String hospitalCode;
    private String hospitalName;
    private String hospitalizationTimes;
    private String inpatientNo;
    private String kpsScore;
    private String lengthOfStay;
    private String medicalRecordNo;
    private String patientId;
    private String patientIdOld;
    private LocalDateTime recordDatetime;
    private String recordSn;
    private Integer recordStatus;
    private String recordTemplateName;
    private String recordText;
    private String recordTitle;
    private LocalDateTime recordUpdateDatetime;
    private String signatureDoctor;
    private String treatmentInfo;
    private String visitSn;
    private LocalDateTime yyCollectionDatetime;
    private Long yyRecordId;
    private String yyRecordMd5;
    private Integer yyUploadStatus;
    private LocalDateTime yyEtlTime;
    private LocalDateTime yyUploadTime;
    private String yyBatchTime;
    private String yyRecordBatchId;
    private LocalDateTime yyBackfillTime;
    private Integer yyBackfillStatus;
    private String branchCode;
    private String branchName;
    private LocalDateTime dateForPartition;
} 