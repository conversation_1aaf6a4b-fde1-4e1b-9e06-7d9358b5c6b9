package cn.wbw.yyhis.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 用于TNM分期记录的添加和更新操作
 *
 * <AUTHOR>
 * @since 2024-07-07
 */
@Data
public class TnmTStagingUpsertDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * TNM分期记录流水号 (更新时使用)
     */
    private String recordSn;

    /**
     * 单次就诊唯一标识号
     */
    private String visitSn;

    /**
     * 临床诊断
     */
    private String diagnosisItemCode;

    /**
     * 肿瘤分期-T
     */
    private String tnmTStaging;

    /**
     * 肿瘤分期-N
     */
    private String tnmNStaging;

    /**
     * 肿瘤分期-M
     */
    private String tnmMStaging;

    /**
     * FIGO分期-宫颈癌
     */
    private String figoStagingCervicalCancer;

    /**
     * CNLC分期-肝细胞癌
     */
    private String cnlcStagingHepatocellular;

    /**
     * Ann Arbor分期-淋巴瘤
     */
    private String annArborStagingLymphoma;

    /**
     * 分期时间
     */
    private LocalDateTime stagingDatetime;
}
