package cn.wbw.yyhis.model.dto;

import lombok.Data;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * B032 DTO
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@Data
public class B032DTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String admissionBedCode;
    private String admissionBedName;
    private LocalDateTime admissionDatetime;
    private String admissionDeptCode;
    private String admissionDeptName;
    private String admissionMedicalTeamCode;
    private String admissionMedicalTeamName;
    private String admissionTypeCode;
    private String admissionTypeName;
    private String admissionWardCode;
    private String admissionWardName;
    private String attendingPhysician;
    private String attendingPhysicianId;
    private String chiefPhysician;
    private String chiefPhysicianId;
    private String currentBedCode;
    private String currentBedName;
    private String currentDeptCode;
    private String currentDeptName;
    private String currentWardCode;
    private String currentWardName;
    private String extendData1;
    private String extendData2;
    private String fromTable;
    private String fromYyRecordId;
    private String hospitalCode;
    private String hospitalName;
    private String hospitalizationTimes;
    private String inpatientNo;
    private String medicalRecordNo;
    private String name;
    private String patientId;
    private String patientIdOld;
    private LocalDateTime recordDatetime;
    private Integer recordStatus;
    private LocalDateTime recordUpdateDatetime;
    private String responsibleNurse;
    private String responsibleNurseId;
    private String visitCardNo;
    private String visitDoctorName;
    private String visitDoctorNo;
    private String visitSn;
    private LocalDateTime yyCollectionDatetime;
    private Long yyRecordId;
    private String yyRecordMd5;
    private Integer yyUploadStatus;
    private LocalDateTime yyEtlTime;
    private LocalDateTime yyUploadTime;
    private String yyBatchTime;
    private String yyRecordBatchId;
    private LocalDateTime yyBackfillTime;
    private Integer yyBackfillStatus;
    private String branchCode;
    private String branchName;
    private LocalDateTime dateForPartition;
} 