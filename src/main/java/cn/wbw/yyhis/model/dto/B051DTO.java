package cn.wbw.yyhis.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * B051 DTO
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@Data
public class B051DTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String auxiliaryExam;
    private String bodySurfaceArea;
    private String bodyTemperature;
    private String chiefComplaint;
    private String confirmedDiagnosis;
    private String contentType;
    private String currentMedhistory;
    private String diastolicPressure;
    private String ecogScore;
    private String familyHistory;
    private String fromTable;
    private String fromYyRecordId;
    private String heartRate;
    private String height;
    private String hospitalCode;
    private String hospitalName;
    private String hospitalizationTimes;
    private String inpatientNo;
    private String kpsScore;
    private String marriageBirthHistory;
    private String medicalRecordNo;
    private String menstrualHistory;
    private String otherDiagnosis;
    private String pastMedhistory;
    private String patientId;
    private String patientIdOld;
    private String personalMedhistory;
    private String physicalExam;
    private String primaryDiagnosis;
    private LocalDateTime recordDatetime;
    private String recordSn;
    private Integer recordStatus;
    private String recordTemplateName;
    private String recordText;
    private String recordTitle;
    private LocalDateTime recordUpdateDatetime;
    private String respiratoryRate;
    private String revisionDiagnosis;
    private String signatureDoctor;
    private String specialExam;
    private String systolicPressure;
    private String visitSn;
    private String weight;
    private LocalDateTime yyCollectionDatetime;
    private Long yyRecordId;
    private String yyRecordMd5;
    private Integer yyUploadStatus;
    private LocalDateTime yyEtlTime;
    private LocalDateTime yyUploadTime;
    private String yyBatchTime;
    private String yyRecordBatchId;
    private LocalDateTime yyBackfillTime;
    private Integer yyBackfillStatus;
    private String branchCode;
    private String branchName;
    private LocalDateTime dateForPartition;
} 