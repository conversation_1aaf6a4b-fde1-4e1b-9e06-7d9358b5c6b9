package cn.wbw.yyhis.model.dto;

import lombok.Data;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 用于B0617(术后首次病程)的添加和更新操作
 */
@Data
public class B0617UpsertDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 病程记录流水号 (更新时使用)
     */
    private String recordSn;

    /**
     * 单次就诊唯一标识号
     */
    private String visitSn;

    /**
     * 术后诊断
     */
    private String postOperationDiagnosis;

    /**
     * 手术名称
     */
    private String surgeryName;

    /**
     * 麻醉方式
     */
    private String anesthesiaMethod;

    /**
     * 手术开始时间
     */
    private LocalDateTime surgeryStartDatetime;

    /**
     * 手术结束时间
     */
    private LocalDateTime surgeryEndDatetime;

    /**
     * 术后情况/手术经过
     */
    private String surgeryProcess;

    /**
     * 术后处理措施
     */
    private String postoperationTreatment;

    /**
     * 术后注意事项
     */
    private String mattersNeedCaution;

} 