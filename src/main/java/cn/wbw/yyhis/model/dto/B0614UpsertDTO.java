package cn.wbw.yyhis.model.dto;

import lombok.Data;
import java.io.Serializable;

/**
 * 用于B0614(术前小结)的添加和更新操作
 */
@Data
public class B0614UpsertDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 病程记录流水号 (更新时使用)
     */
    private String recordSn;

    /**
     * 单次就诊唯一标识号
     */
    private String visitSn;

    /**
     * 术前诊断
     */
    private String preOperativeDiagnosis;

    /**
     * 诊断依据
     */
    private String diagnosisBasis;

    /**
     * 手术指征
     */
    private String operationIndication;

    /**
     * 拟施手术名称
     */
    private String plannedSurgeryName;

    /**
     * 拟施手术方式
     */
    private String surgeryMethod;

    /**
     * 拟施麻醉方式
     */
    private String anesthesiaMethod;

    /**
     * 注意事项
     */
    private String possibleProblemsAndSolution;

} 