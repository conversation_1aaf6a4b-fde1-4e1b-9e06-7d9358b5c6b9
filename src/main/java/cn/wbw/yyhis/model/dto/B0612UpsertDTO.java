package cn.wbw.yyhis.model.dto;

import lombok.Data;
import java.io.Serializable;

/**
 * 用于B0612(会诊记录)的添加和更新操作
 */
@Data
public class B0612UpsertDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 病程记录流水号 (更新时使用)
     */
    private String recordSn;

    /**
     * 单次就诊唯一标识号
     */
    private String visitSn;

    /**
     * 会诊申请日期
     */
    private String consApplyTime;

    /**
     * 会诊日期
     */
    private String consTime;

    /**
     * 诊断名称
     */
    private String diagnosisConsultationCorrected;

    /**
     * 辅助检查
     */
    private String auxiliaryExam;

    /**
     * 患者病情
     */
    private String recordAbstract;

    /**
     * 诊疗情况
     */
    private String treatProDescription;

    /**
     * 会诊原因及目的
     */
    private String consReason;

    /**
     * 邀请科室
     */
    private String deptInvited;

    /**
     * 会诊意见
     */
    private String consRecommendation;
} 