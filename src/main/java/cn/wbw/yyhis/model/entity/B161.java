package cn.wbw.yyhis.model.entity;

import cn.wbw.yyhis.annotation.CompositeKey;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 常规检查记录
 */
@Data
@TableName("b16_1")
public class B161 {
    public static final String TABLE_NAME = "b16_1";

    @TableField("apply_datetime")
    private String applyDatetime;

    @TableField("apply_dept_code")
    private String applyDeptCode;

    @TableField("apply_dept_name")
    private String applyDeptName;

    @TableField("apply_no")
    private String applyNo;

    @TableField("exam_datetime")
    private String examDatetime;

    @TableField("exam_diag_conclusion")
    private String examDiagConclusion;

    @TableField("exam_diag_description")
    private String examDiagDescription;

    @TableField("exam_doc_code")
    private String examDocCode;

    @TableField("exam_doc_name")
    private String examDocName;

    @TableField("exam_item_code")
    private String examItemCode;

    @TableField("exam_item_name")
    private String examItemName;

    @TableField("exam_item_type")
    private String examItemType;

    @TableField("exam_sites")
    private String examSites;

    @TableField("exam_sn")
    private String examSn;

    @TableField("exam_type")
    private String examType;

    @TableField("extend_data1")
    private String extendData1;

    @TableField("extend_data2")
    private String extendData2;

    @TableField("from_table")
    private String fromTable;

    @TableField("from_yy_record_id")
    private String fromYyRecordId;

    @TableField("hospital_code")
    @CompositeKey(order = 1, name = "hospital_code")
    private String hospitalCode;

    @TableField("hospital_name")
    private String hospitalName;

    @TableField("hospitalization_times")
    private String hospitalizationTimes;

    @TableField("inpatient_no")
    private String inpatientNo;

    @TableField("medical_record_no")
    private String medicalRecordNo;

    @TableField("name")
    private String name;

    @TableField("order_sn")
    private String orderSn;

    @TableField("outpatient_no")
    private String outpatientNo;

    @TableField("patient_id")
    private String patientId;

    @TableField("patient_id_old")
    private String patientIdOld;

    @TableField("qualitive_description")
    private String qualitiveDescription;

    @TableField("record_datetime")
    private String recordDatetime;

    @TableField("record_status")
    private Integer recordStatus;

    @TableField("record_update_datetime")
    private LocalDateTime recordUpdateDatetime;

    @TableField("report_datetime")
    private String reportDatetime;

    @TableField("report_dept_code")
    private String reportDeptCode;

    @TableField("report_dept_name")
    private String reportDeptName;

    @TableField("report_doc_code")
    private String reportDocCode;

    @TableField("report_doc_name")
    private String reportDocName;

    @TableId(value = "report_no", type = IdType.INPUT)
    @CompositeKey(order = 3, name = "report_no")
    private String reportNo;

    @TableField("verify_doc_code")
    private String verifyDocCode;

    @TableField("verify_doc_name")
    private String verifyDocName;

    @TableField("visit_card_no")
    private String visitCardNo;

    @TableField("visit_sn")
    @CompositeKey(order = 2, name = "visit_sn")
    private String visitSn;

    @TableField("visit_times")
    private String visitTimes;

    @TableField("visit_type")
    private String visitType;

    @TableField("visit_sn_old")
    @CompositeKey(order = 4, name = "visit_sn_old")
    private String visitSnOld;

    @TableField("yy_collection_datetime")
    private LocalDateTime yyCollectionDatetime;

    @TableField("yy_record_id")
    private Long yyRecordId;

    @TableField("yy_record_md5")
    private String yyRecordMd5;

    @TableField("yy_upload_status")
    private Integer yyUploadStatus;

    @TableField("yy_etl_time")
    private LocalDateTime yyEtlTime;

    @TableField("yy_upload_time")
    private LocalDateTime yyUploadTime;

    @TableField("yy_batch_time")
    private String yyBatchTime;

    @TableField("yy_record_batch_id")
    private String yyRecordBatchId;

    @TableField("yy_backfill_time")
    private LocalDateTime yyBackfillTime;

    @TableField("yy_backfill_status")
    private Integer yyBackfillStatus;

    @TableField("branch_code")
    private String branchCode;

    @TableField("branch_name")
    private String branchName;

    @TableField("date_for_partition")
    private LocalDateTime dateForPartition;
} 