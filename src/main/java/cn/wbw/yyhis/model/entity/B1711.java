package cn.wbw.yyhis.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * <p>
 * 常规检验记录（明细）
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-26
 */
@Data
@TableName("b17_1_1")
public class B1711 implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 申请时间
     */
    @TableField("apply_datetime")
    private String applyDatetime;

    /**
     * 申请科室代码
     */
    @TableField("apply_dept_code")
    private String applyDeptCode;

    /**
     * 申请科室名称
     */
    @TableField("apply_dept_name")
    private String applyDeptName;

    /**
     * 申请单号
     */
    @TableField("apply_no")
    private String applyNo;

    /**
     * 检查医生代码
     */
    @TableField("exam_doc_code")
    private String examDocCode;

    /**
     * 检查医生姓名
     */
    @TableField("exam_doc_name")
    private String examDocName;

    /**
     * 扩展字段1
     */
    @TableField("extend_data1")
    private String extendData1;

    /**
     * 扩展字段2
     */
    @TableField("extend_data2")
    private String extendData2;

    /**
     * B17_1唯一数据标识report_no关联
     */
    @TableField("fk_report_no")
    private String fkReportNo;

    /**
     * 来源表
     */
    @TableField("from_table")
    private String fromTable;

    /**
     * 来源数据唯一标识
     */
    @TableField("from_yy_record_id")
    private String fromYyRecordId;

    /**
     * 组织机构代码
     */
    @TableField("hospital_code")
    private String hospitalCode;

    /**
     * 医疗机构名称
     */
    @TableField("hospital_name")
    private String hospitalName;

    /**
     * 住院次数
     */
    @TableField("hospitalization_times")
    private String hospitalizationTimes;

    /**
     * 住院号
     */
    @TableField("inpatient_no")
    private String inpatientNo;

    /**
     * 明细项目代码
     */
    @TableField("item_code")
    private String itemCode;

    /**
     * 组合项目代码
     */
    @TableField("item_group_code")
    private String itemGroupCode;

    /**
     * 组合项目名称
     */
    @TableField("item_group_name")
    private String itemGroupName;

    /**
     * 明细项目名称
     */
    @TableField("item_name")
    private String itemName;

    /**
     * 明细项目序号
     */
    @TableField("item_no")
    private String itemNo;

    /**
     * 检验细项流水号
     */
    @TableId(value = "lab_item_sn", type = IdType.INPUT)
    private String labItemSn;

    /**
     * 检验流水号
     */
    @TableField("lab_sn")
    private String labSn;

    /**
     * 检验类型
     */
    @TableField("lab_type")
    private String labType;

    /**
     * 病案号
     */
    @TableField("medical_record_no")
    private String medicalRecordNo;

    /**
     * 患者姓名
     */
    @TableField("name")
    private String name;

    /**
     * 申请医嘱流水号
     */
    @TableField("order_sn")
    private String orderSn;

    /**
     * 门诊号
     */
    @TableField("outpatient_no")
    private String outpatientNo;

    /**
     * 患者ID
     */
    @TableField("patient_id")
    private String patientId;

    /**
     * 患者原始ID
     */
    @TableField("patient_id_old")
    private String patientIdOld;

    /**
     * 记录创建时间
     */
    @TableField("record_datetime")
    private String recordDatetime;

    /**
     * 记录状态
     */
    @TableField("record_status")
    private Integer recordStatus;

    /**
     * 记录更新时间
     */
    @TableField("record_update_datetime")
    private LocalDateTime recordUpdateDatetime;

    /**
     * 结果参考值范围
     */
    @TableField("reference_range")
    private String referenceRange;

    /**
     * 参考值提醒
     */
    @TableField("reference_range_alert")
    private String referenceRangeAlert;

    /**
     * 参考值高值
     */
    @TableField("reference_range_high")
    private String referenceRangeHigh;

    /**
     * 参考值低值
     */
    @TableField("reference_range_low")
    private String referenceRangeLow;

    /**
     * 报告时间
     */
    @TableField("report_datetime")
    private String reportDatetime;

    /**
     * 填报医生编码
     */
    @TableField("report_doc_code")
    private String reportDocCode;

    /**
     * 填报医生姓名
     */
    @TableField("report_doc_name")
    private String reportDocName;

    /**
     * 报告单号(与b17_1关联)
     */
    @TableField("report_no")
    private String reportNo;

    /**
     * 检验-结果(未区分定性/定量)
     */
    @TableField("result_hybrid")
    private String resultHybrid;

    /**
     * 检验-结果(定性)
     */
    @TableField("result_qualitative")
    private String resultQualitative;

    /**
     * 检验-结果(数值)
     */
    @TableField("result_quantitative")
    private String resultQuantitative;

    /**
     * 检验-计量单位
     */
    @TableField("result_unit")
    private String resultUnit;

    /**
     * 标本编号
     */
    @TableField("specimen_code")
    private String specimenCode;

    /**
     * 标本类型代码
     */
    @TableField("specimen_type_code")
    private String specimenTypeCode;

    /**
     * 标本类型名称
     */
    @TableField("specimen_type_name")
    private String specimenTypeName;

    /**
     * 检验时间
     */
    @TableField("test_datetime")
    private String testDatetime;

    /**
     * 检验方法
     */
    @TableField("test_method")
    private String testMethod;

    /**
     * 检验方法代码
     */
    @TableField("test_method_code")
    private String testMethodCode;

    /**
     * 审核医师代码
     */
    @TableField("verify_doc_code")
    private String verifyDocCode;

    /**
     * 审核医生姓名
     */
    @TableField("verify_doc_name")
    private String verifyDocName;

    /**
     * 就诊卡号
     */
    @TableField("visit_card_no")
    private String visitCardNo;

    /**
     * 单次就诊唯一标识号
     */
    @TableField("visit_sn")
    private String visitSn;

    /**
     * 就诊次数
     */
    @TableField("visit_times")
    private String visitTimes;

    /**
     * 就诊类型
     */
    @TableField("visit_type")
    private String visitType;

    /**
     * 单次就诊唯一标识号（原始）
     */
    @TableField("visit_sn_old")
    private String visitSnOld;

    /**
     * 数据采集时间
     */
    @TableField("yy_collection_datetime")
    private LocalDateTime yyCollectionDatetime;

    /**
     * 数据唯一标识
     */
    @TableField(value = "yy_record_id")
    private Long yyRecordId;

    /**
     * 数据行唯一记录
     */
    @TableField("yy_record_md5")
    private String yyRecordMd5;

    /**
     * 上报状态
     */
    @TableField("yy_upload_status")
    private Integer yyUploadStatus;

    /**
     * 抽取完成时间
     */
    @TableField("yy_etl_time")
    private LocalDateTime yyEtlTime;

    /**
     * 上报完成时间
     */
    @TableField("yy_upload_time")
    private LocalDateTime yyUploadTime;

    /**
     * 上报批次号
     */
    @TableField("yy_batch_time")
    private String yyBatchTime;

    /**
     * 数据插入唯一号
     */
    @TableField("yy_record_batch_id")
    private String yyRecordBatchId;

    /**
     * 数据反填时间
     */
    @TableField("yy_backfill_time")
    private LocalDateTime yyBackfillTime;

    /**
     * 数据反填状态
     */
    @TableField("yy_backfill_status")
    private Integer yyBackfillStatus;

    /**
     * 分院编码
     */
    @TableField("branch_code")
    private String branchCode;

    /**
     * 分院名称
     */
    @TableField("branch_name")
    private String branchName;

    /**
     * 分区用业务日期
     */
    @TableField("date_for_partition")
    private LocalDateTime dateForPartition;
} 