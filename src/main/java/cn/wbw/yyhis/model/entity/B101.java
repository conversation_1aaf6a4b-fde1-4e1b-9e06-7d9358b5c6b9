package cn.wbw.yyhis.model.entity;

import cn.wbw.yyhis.annotation.CompositeKey;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 住院医嘱记录
 */
@Data
@TableName("b10_1")
public class B101 {
    public static final String TABLE_NAME = "b10_1";

    @TableField("administration_route")
    private String administrationRoute;

    @TableField("antibiotics_level")
    private String antibioticsLevel;

    @TableField("dose")
    private String dose;

    @TableField("dose_unit")
    private String doseUnit;

    @TableField("drug_compatibility")
    private String drugCompatibility;

    @TableField("drug_flag")
    private String drugFlag;

    @TableField("drug_order")
    private String drugOrder;

    @TableField("executive_dept_code")
    private String executiveDeptCode;

    @TableField("executive_dept_name")
    private String executiveDeptName;

    @TableField("extend_data1")
    private String extendData1;

    @TableField("extend_data2")
    private String extendData2;

    @TableField("form")
    private String form;

    @TableField("frequency_code")
    private String frequencyCode;

    @TableField("frequency_name")
    private String frequencyName;

    @TableField("from_table")
    private String fromTable;

    @TableField("from_yy_record_id")
    private String fromYyRecordId;

    @TableField("herbal_note")
    private String herbalNote;

    @TableField("hospital_code")
    @CompositeKey(order = 1, name = "hospital_code")
    private String hospitalCode;

    @TableField("hospital_name")
    private String hospitalName;

    @TableField("hospitalization_times")
    private String hospitalizationTimes;

    @TableField("infusion_duration")
    private String infusionDuration;

    @TableField("injection_type")
    private String injectionType;

    @TableField("inpatient_no")
    private String inpatientNo;

    @TableField("item_total_price")
    private String itemTotalPrice;

    @TableField("item_unit_price")
    private String itemUnitPrice;

    @TableField("job_title")
    private String jobTitle;

    @TableField("liquid_configuration")
    private String liquidConfiguration;

    @TableField("manufac")
    private String manufac;

    @TableField("medical_record_no")
    private String medicalRecordNo;

    @TableField("num_of_order")
    private String numOfOrder;

    @TableField("num_of_order_unit")
    private String numOfOrderUnit;

    @TableField("operation_id")
    private String operationId;

    @TableField("order_class_code")
    private String orderClassCode;

    @TableField("order_class_name")
    private String orderClassName;

    @TableField("order_confirm_time")
    private String orderConfirmTime;

    @TableField("order_dept_code")
    private String orderDeptCode;

    @TableField("order_dept_name")
    private String orderDeptName;

    @TableField("order_doctor_name")
    private String orderDoctorName;

    @TableField("order_doctor_no")
    private String orderDoctorNo;

    @TableField("order_end_datetime")
    private String orderEndDatetime;

    @TableField("order_given_time")
    private String orderGivenTime;

    @TableField("order_group_no")
    private String orderGroupNo;

    @TableField("order_item_code")
    private String orderItemCode;

    @TableField("order_item_name")
    @CompositeKey(order = 4, name = "order_item_name")
    private String orderItemName;

    @TableField("order_note")
    private String orderNote;

    @TableId(value = "order_sn", type = IdType.INPUT)
    @CompositeKey(order = 3, name = "order_sn")
    private String orderSn;

    @TableField("order_start_datetime")
    private String orderStartDatetime;

    @TableField("order_status")
    private String orderStatus;

    @TableField("order_type")
    private String orderType;

    @TableField("patient_id")
    private String patientId;

    @TableField("patient_id_old")
    private String patientIdOld;

    @TableField("record_datetime")
    private String recordDatetime;

    @TableField("record_status")
    private Integer recordStatus;

    @TableField("record_update_datetime")
    private LocalDateTime recordUpdateDatetime;

    @TableField("request_no")
    private String requestNo;

    @TableField("spec")
    private String spec;

    @TableField("special_order_flag")
    private String specialOrderFlag;

    @TableField("technical_title")
    private String technicalTitle;

    @TableField("trade_name")
    private String tradeName;

    @TableField("visit_dept_code")
    private String visitDeptCode;

    @TableField("visit_dept_name")
    private String visitDeptName;

    @TableField("visit_sn")
    @CompositeKey(order = 2, name = "visit_sn")
    private String visitSn;

    @TableField("yy_collection_datetime")
    private LocalDateTime yyCollectionDatetime;

    @TableField("yy_record_id")
    private Long yyRecordId;

    @TableField("yy_record_md5")
    private String yyRecordMd5;

    @TableField("yy_upload_status")
    private Integer yyUploadStatus;

    @TableField("yy_etl_time")
    private LocalDateTime yyEtlTime;

    @TableField("yy_upload_time")
    private LocalDateTime yyUploadTime;

    @TableField("yy_batch_time")
    private String yyBatchTime;

    @TableField("yy_record_batch_id")
    private String yyRecordBatchId;

    @TableField("yy_backfill_time")
    private LocalDateTime yyBackfillTime;

    @TableField("yy_backfill_status")
    private Integer yyBackfillStatus;

    @TableField("branch_code")
    private String branchCode;

    @TableField("branch_name")
    private String branchName;

    @TableField("date_for_partition")
    private LocalDateTime dateForPartition;
} 