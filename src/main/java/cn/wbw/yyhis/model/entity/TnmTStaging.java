package cn.wbw.yyhis.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * TNM分期记录
 *
 * <AUTHOR>
 * @since 2024-07-07
 */
@Data
@TableName("tnm_tnm_t_staging")
public class TnmTStaging implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 临床诊断
     */
    @TableField("diagnosis_item_code")
    private String diagnosisItemCode;

    /**
     * 肿瘤分期-T
     */
    @TableField("tnm_t_staging")
    private String tnmTStaging;

    /**
     * 肿瘤分期-N
     */
    @TableField("tnm_n_staging")
    private String tnmNStaging;

    /**
     * 肿瘤分期-M
     */
    @TableField("tnm_m_staging")
    private String tnmMStaging;

    /**
     * FIGO分期-宫颈癌
     */
    @TableField("figo_staging_cervical_cancer")
    private String figoStagingCervicalCancer;

    /**
     * CNLC分期-肝细胞癌
     */
    @TableField("cnlc_staging_hepatocellular")
    private String cnlcStagingHepatocellular;

    /**
     * Ann Arbor分期-淋巴瘤
     */
    @TableField("ann_arbor_staging_lymphoma")
    private String annArborStagingLymphoma;

    /**
     * 分期时间
     */
    @TableField("staging_datetime")
    private LocalDateTime stagingDatetime;

    /**
     * 记录创建时间
     */
    @TableField("record_datetime")
    private LocalDateTime recordDatetime;

    /**
     * TNM分期记录流水号
     */
    @TableId(value = "record_sn", type = IdType.INPUT)
    private String recordSn;

    /**
     * 记录更新时间
     */
    @TableField("record_update_datetime")
    private LocalDateTime recordUpdateDatetime;

    /**
     * 单次就诊唯一标识号
     */
    @TableField("visit_sn")
    private String visitSn;
}
