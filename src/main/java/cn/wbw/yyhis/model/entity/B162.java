package cn.wbw.yyhis.model.entity;

import cn.wbw.yyhis.annotation.CompositeKey;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * <p>
 * 病理检查记录
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-26
 */
@Data
@TableName("b16_2")
public class B162 implements Serializable {

    private static final long serialVersionUID = 1L;

    public static final String TABLE_NAME = "b16_2";

    /**
     * 年龄(岁)
     */
    @TableField("age")
    private String age;

    /**
     * 申请时间
     */
    @TableField("apply_datetime")
    private String applyDatetime;

    /**
     * 申请单号
     */
    @TableField("apply_no")
    private String applyNo;

    /**
     * 血管浸润
     */
    @TableField("blood_vessel_infil")
    private String bloodVesselInfil;

    /**
     * 病理分期
     */
    @TableField("clinical_stage")
    private String clinicalStage;

    /**
     * 标本采集时间
     */
    @TableField("collected_datetime")
    private String collectedDatetime;

    /**
     * 出生日期
     */
    @TableField("date_of_birth")
    private String dateOfBirth;

    /**
     * 分化程度
     */
    @TableField("differentiation_degree")
    private String differentiationDegree;

    /**
     * 检测时间
     */
    @TableField("exam_datetime")
    private String examDatetime;

    /**
     * 检查医生代码
     */
    @TableField("exam_doc_code")
    private String examDocCode;

    /**
     * 检查医生姓名
     */
    @TableField("exam_doc_name")
    private String examDocName;

    /**
     * 扩展字段1
     */
    @TableField("extend_data1")
    private String extendData1;

    /**
     * 扩展字段2
     */
    @TableField("extend_data2")
    private String extendData2;

    /**
     * 来源表
     */
    @TableField("from_table")
    private String fromTable;

    /**
     * 来源数据唯一标识
     */
    @TableField("from_yy_record_id")
    private String fromYyRecordId;

    /**
     * 性别
     */
    @TableField("gender")
    private String gender;

    /**
     * 肉眼所见
     */
    @TableField("gross_description")
    private String grossDescription;

    /**
     * 组织机构代码
     */
    @TableField("hospital_code")
    @CompositeKey(order = 1, name = "hospital_code")
    private String hospitalCode;

    /**
     * 医疗机构名称
     */
    @TableField("hospital_name")
    private String hospitalName;

    /**
     * 住院次数
     */
    @TableField("hospitalization_times")
    private String hospitalizationTimes;

    /**
     * 免疫组化检测结果
     */
    @TableField("ihc_results")
    private String ihcResults;

    /**
     * 住院号
     */
    @TableField("inpatient_no")
    private String inpatientNo;

    /**
     * 术中冰冻结果
     */
    @TableField("intraoperative_freezing_result")
    private String intraoperativeFreezingResult;

    /**
     * 淋巴浸润
     */
    @TableField("lymph_infilt")
    private String lymphInfilt;

    /**
     * 淋巴结转移
     */
    @TableField("lymph_met")
    private String lymphMet;

    /**
     * 病案号
     */
    @TableField("medical_record_no")
    private String medicalRecordNo;

    /**
     * 镜下所见
     */
    @TableField("micro_description")
    private String microDescription;

    /**
     * 分子检测项目及方法
     */
    @TableField("mol_test_gene_and_method")
    private String molTestGeneAndMethod;

    /**
     * 有无分子检测
     */
    @TableField("mol_test_mark")
    private String molTestMark;

    /**
     * 分子检测结果
     */
    @TableField("mol_test_result")
    private String molTestResult;

    /**
     * 患者姓名
     */
    @TableField("name")
    private String name;

    /**
     * 神经侵犯
     */
    @TableField("nerve_invasion")
    private String nerveInvasion;

    /**
     * 申请医嘱流水号
     */
    @TableField("order_sn")
    private String orderSn;

    /**
     * 门诊号
     */
    @TableField("outpatient_no")
    private String outpatientNo;

    /**
     * 病理诊断结论
     */
    @TableField("patho_diag_conclusion")
    private String pathoDiagConclusion;

    /**
     * 病理号
     */
    @TableField("pathology_no")
    private String pathologyNo;

    /**
     * 病理检查流水号
     */
    @TableField("pathology_sn")
    private String pathologySn;

    /**
     * 病理检查类型
     */
    @TableField("pathology_test_type")
    private String pathologyTestType;

    /**
     * 病理检查类型代码
     */
    @TableField("pathology_test_type_code")
    private String pathologyTestTypeCode;

    /**
     * 患者ID
     */
    @TableField("patient_id")
    private String patientId;

    /**
     * 患者原始ID
     */
    @TableField("patient_id_old")
    private String patientIdOld;

    /**
     * 标本接收时间
     */
    @TableField("received_datetime")
    private String receivedDatetime;

    /**
     * 记录创建时间
     */
    @TableField("record_datetime")
    private String recordDatetime;

    /**
     * 记录状态
     */
    @TableField("record_status")
    private Integer recordStatus;

    /**
     * 记录更新时间
     */
    @TableField("record_update_datetime")
    private LocalDateTime recordUpdateDatetime;

    /**
     * 记录人1
     */
    @TableField("recorder1")
    private String recorder1;

    /**
     * 记录人2
     */
    @TableField("recorder2")
    private String recorder2;

    /**
     * 报告时间
     */
    @TableField("report_datetime")
    private String reportDatetime;

    /**
     * 报告科室代码
     */
    @TableField("report_dept_code")
    private String reportDeptCode;

    /**
     * 报告科室名称
     */
    @TableField("report_dept_name")
    private String reportDeptName;

    /**
     * 报告医生代码
     */
    @TableField("report_doc_code")
    private String reportDocCode;

    /**
     * 报告医生姓名
     */
    @TableField("report_doc_name")
    private String reportDocName;

    /**
     * 报告单号
     */
    @TableId(value = "report_no", type = IdType.INPUT)
    @CompositeKey(order = 3, name = "report_no")
    private String reportNo;

    /**
     * 标本编号
     */
    @TableField("specimen_code")
    private String specimenCode;

    /**
     * 标本来源
     */
    @TableField("specimen_obtain_method")
    private String specimenObtainMethod;

    /**
     * 标本部位
     */
    @TableField("specimen_site")
    private String specimenSite;

    /**
     * 标本类型
     */
    @TableField("specimen_type")
    private String specimenType;

    /**
     * TNM分期版本
     */
    @TableField("staging_version")
    private String stagingVersion;

    /**
     * TNM分期-M
     */
    @TableField("tnm_m_staging")
    private String tnmMStaging;

    /**
     * TNM分期-N
     */
    @TableField("tnm_n_staging")
    private String tnmNStaging;

    /**
     * TNM分期-T
     */
    @TableField("tnm_t_staging")
    private String tnmTStaging;

    /**
     * 脉管瘤栓
     */
    @TableField("vasc_tumour_thrombus")
    private String vascTumourThrombus;

    /**
     * 审核医师代码
     */
    @TableField("verify_doc_code")
    private String verifyDocCode;

    /**
     * 审核医生姓名
     */
    @TableField("verify_doc_name")
    private String verifyDocName;

    /**
     * 就诊卡号
     */
    @TableField("visit_card_no")
    private String visitCardNo;

    /**
     * 单次就诊唯一标识号
     */
    @TableField("visit_sn")
    @CompositeKey(order = 2, name = "visit_sn")
    private String visitSn;

    /**
     * 就诊次数
     */
    @TableField("visit_times")
    private String visitTimes;

    /**
     * 就诊类型
     */
    @TableField("visit_type")
    private String visitType;

    /**
     * 单次就诊唯一标识号（原始）
     */
    @TableField("visit_sn_old")
    @CompositeKey(order = 4, name = "visit_sn_old")
    private String visitSnOld;

    /**
     * 数据采集时间
     */
    @TableField("yy_collection_datetime")
    private LocalDateTime yyCollectionDatetime;

    /**
     * 数据唯一标识
     */
    @TableField("yy_record_id")
    private Long yyRecordId;

    /**
     * 数据行唯一记录
     */
    @TableField("yy_record_md5")
    private String yyRecordMd5;

    /**
     * 上报状态
     */
    @TableField("yy_upload_status")
    private Integer yyUploadStatus;

    /**
     * 抽取完成时间
     */
    @TableField("yy_etl_time")
    private LocalDateTime yyEtlTime;

    /**
     * 上报完成时间
     */
    @TableField("yy_upload_time")
    private LocalDateTime yyUploadTime;

    /**
     * 上报批次号
     */
    @TableField("yy_batch_time")
    private String yyBatchTime;

    /**
     * 数据插入唯一号
     */
    @TableField("yy_record_batch_id")
    private String yyRecordBatchId;

    /**
     * 数据反填时间
     */
    @TableField("yy_backfill_time")
    private LocalDateTime yyBackfillTime;

    /**
     * 数据反填状态
     */
    @TableField("yy_backfill_status")
    private Integer yyBackfillStatus;

    /**
     * 分院编码
     */
    @TableField("branch_code")
    private String branchCode;

    /**
     * 分院名称
     */
    @TableField("branch_name")
    private String branchName;

    /**
     * 分区用业务日期
     */
    @TableField("date_for_partition")
    private LocalDateTime dateForPartition;
} 