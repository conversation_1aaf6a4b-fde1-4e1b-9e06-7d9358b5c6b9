package cn.wbw.yyhis.model.entity;

import cn.wbw.yyhis.annotation.CompositeKey;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 术前讨论
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@Data
@TableName("b06_1_3")
public class B0613 implements Serializable {

    private static final long serialVersionUID = 1L;

    public static final String TABLE_NAME = "b06_1_3";

    /**
     * 文书内容传递格式
     */
    private String contentType;

    /**
     * 讨论日期
     */
    private String discussionDatetime;

    /**
     * 讨论意见
     */
    private String discussionOpinion;

    /**
     * 扩展字段1
     */
    private String extendData1;

    /**
     * 扩展字段2
     */
    private String extendData2;

    /**
     * 来源表
     */
    private String fromTable;

    /**
     * 来源数据唯一标识
     */
    private String fromYyRecordId;

    /**
     * 组织机构代码
     */
    @CompositeKey(order = 1, name = "hospital_code")
    private String hospitalCode;

    /**
     * 医疗机构名称
     */
    private String hospitalName;

    /**
     * 住院次数
     */
    private String hospitalizationTimes;

    /**
     * 讨论小结
     */
    private String hostConclusion;

    /**
     * 住院号
     */
    private String inpatientNo;

    /**
     * 病程时间
     */
    @CompositeKey(order = 4, name = "medical_note_date")
    private String medicalNoteDate;

    /**
     * 病案号
     */
    private String medicalRecordNo;

    /**
     * 手术指征
     */
    private String operationIndication;

    /**
     * 手术步骤
     */
    private String operationSetps;

    /**
     * 参加人员
     */
    private String participantsList;

    /**
     * 患者ID
     */
    private String patientId;

    /**
     * 患者原始ID
     */
    private String patientIdOld;

    /**
     * 术中注意事项
     */
    private String possibleProblemsAndSolution;

    /**
     * 术前准备
     */
    private String preoperationPreparation;

    /**
     * 记录创建时间
     */
    private LocalDateTime recordDatetime;

    /**
     * 病程记录流水号
     */
    @TableId(value = "record_sn", type = IdType.INPUT)
    private String recordSn;

    /**
     * 记录状态
     */
    private Integer recordStatus;

    /**
     * 文本模板名称
     */
    private String recordTemplateName;

    /**
     * 文书内容
     */
    private String recordText;

    /**
     * 病程记录标题
     */
    @CompositeKey(order = 3, name = "record_title")
    private String recordTitle;

    /**
     * 病程记录类型代码
     */
    private String recordTitleCode;

    /**
     * 记录更新时间
     */
    private LocalDateTime recordUpdateDatetime;

    /**
     * 医生签名
     */
    private String signatureDoctor;

    /**
     * 手术方式
     */
    private String surgeryMethod;

    /**
     * 手术体位
     */
    private String surgeryPositions;

    /**
     * 单次就诊唯一标识号
     */
    @CompositeKey(order = 2, name = "visit_sn")
    private String visitSn;

    /**
     * 数据采集时间
     */
    private LocalDateTime yyCollectionDatetime;

    /**
     * 数据唯一标识
     */
    private Long yyRecordId;

    /**
     * 数据行唯一记录
     */
    private String yyRecordMd5;

    /**
     * 上报状态
     */
    private Integer yyUploadStatus;

    /**
     * 抽取完成时间
     */
    private LocalDateTime yyEtlTime;

    /**
     * 上报完成时间
     */
    private LocalDateTime yyUploadTime;

    /**
     * 上报批次号
     */
    private String yyBatchTime;

    /**
     * 数据插入唯一号
     */
    private String yyRecordBatchId;

    /**
     * 数据反填时间
     */
    private LocalDateTime yyBackfillTime;

    /**
     * 数据反填状态
     */
    private Integer yyBackfillStatus;

    /**
     * 分院编码
     */
    private String branchCode;

    /**
     * 分院名称
     */
    private String branchName;

    /**
     * 分区用业务日期
     */
    private LocalDateTime dateForPartition;
} 