package cn.wbw.yyhis.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 患者就诊基本信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@Data
@TableName("b02_1")
public class B021 implements Serializable {

    private static final long serialVersionUID = 1L;
    public static final String TABLE_NAME = "b02_1";

    /**
     * ABO血型
     */
    @TableField("abo_blood_type")
    private String aboBloodType;

    /**
     * 入院时间
     */
    @TableField("admission_datetime")
    private String admissionDatetime;

    /**
     * 血型首位代码
     */
    @TableField("blood_type_s")
    private String bloodTypeS;

    /**
     * 血型末位代码
     */
    @TableField("bolld_type_e")
    private String bolldTypeE;

    /**
     * 证件号码
     */
    @TableField("certificate_no")
    private String certificateNo;

    /**
     * 证件类型
     */
    @TableField("certificate_type")
    private String certificateType;

    /**
     * 紧急联系人1
     */
    @TableField("contact_person1")
    private String contactPerson1;

    /**
     * 紧急联系人2
     */
    @TableField("contact_person2")
    private String contactPerson2;

    /**
     * 紧急联系人电话1
     */
    @TableField("contact_phone_no1")
    private String contactPhoneNo1;

    /**
     * 紧急联系人电话2
     */
    @TableField("contact_phone_no2")
    private String contactPhoneNo2;

    /**
     * 出生日期
     */
    @TableField("date_of_birth")
    private String dateOfBirth;

    /**
     * 出院时间
     */
    @TableField("discharge_datetime")
    private String dischargeDatetime;

    /**
     * 户籍地址-详细地址
     */
    @TableField("domicile_address")
    private String domicileAddress;

    /**
     * 户籍地址-市名称
     */
    @TableField("domicile_city")
    private String domicileCity;

    /**
     * 户籍地址-县名称
     */
    @TableField("domicile_county")
    private String domicileCounty;

    /**
     * 户籍地址-省名称
     */
    @TableField("domicile_province")
    private String domicileProvince;

    /**
     * 文化程度
     */
    @TableField("education")
    private String education;

    /**
     * 文化程度代码
     */
    @TableField("education_code")
    private String educationCode;

    /**
     * 邮箱
     */
    @TableField("email")
    private String email;

    /**
     * 民族
     */
    @TableField("ethnicity")
    private String ethnicity;

    /**
     * 扩展字段1
     */
    @TableField("extend_data1")
    private String extendData1;

    /**
     * 扩展字段2
     */
    @TableField("extend_data2")
    private String extendData2;

    /**
     * 来源表
     */
    @TableField("from_table")
    private String fromTable;

    /**
     * 来源数据唯一标识
     */
    @TableField("from_yy_record_id")
    private String fromYyRecordId;

    /**
     * 性别
     */
    @TableField("gender")
    private String gender;

    /**
     * 健康卡号
     */
    @TableField("health_card_no")
    private String healthCardNo;

    /**
     * 健康卡类型
     */
    @TableField("health_card_type")
    private String healthCardType;

    /**
     * 身高
     */
    @TableField("height")
    private String height;

    /**
     * 现住址
     */
    @TableField("home_address")
    private String homeAddress;

    /**
     * 组织机构代码
     */
    @TableField("hospital_code")
    private String hospitalCode;

    /**
     * 医疗机构名称
     */
    @TableField("hospital_name")
    private String hospitalName;

    /**
     * 住院次数
     */
    @TableField("hospitalization_times")
    private String hospitalizationTimes;

    /**
     * 身份证号码
     */
    @TableField("idcard_no")
    private String idcardNo;

    /**
     * 住院号
     */
    @TableField("inpatient_no")
    private String inpatientNo;

    /**
     * 医保卡号
     */
    @TableField("insurance_no")
    private String insuranceNo;

    /**
     * 费用类型
     */
    @TableField("insurance_type")
    private String insuranceType;

    /**
     * 是否院内感染
     */
    @TableField("is_hospital_infected")
    private String isHospitalInfected;

    /**
     * 婚姻状况类别名称
     */
    @TableField("marital_status")
    private String maritalStatus;

    /**
     * 婚姻状况类别代码
     */
    @TableField("marital_status_code")
    private String maritalStatusCode;

    /**
     * 病案号
     */
    @TableField("medical_record_no")
    private String medicalRecordNo;

    /**
     * 患者姓名
     */
    @TableField("name")
    private String name;

    /**
     * 国籍
     */
    @TableField("nationality")
    private String nationality;

    /**
     * 是否新生儿
     */
    @TableField("newbron_mark")
    private String newbronMark;

    /**
     * 职业类别代码
     */
    @TableField("occupation_code")
    private String occupationCode;

    /**
     * 职业类别名称
     */
    @TableField("occupation_name")
    private String occupationName;

    /**
     * 门诊号
     */
    @TableField("outpatient_no")
    private String outpatientNo;

    /**
     * 性别编码
     */
    @TableField("patient_gender")
    private String patientGender;

    /**
     * 患者ID
     */
    @TableField("patient_id")
    private String patientId;

    /**
     * 患者原始ID
     */
    @TableField("patient_id_old")
    private String patientIdOld;

    /**
     * 患者身份
     */
    @TableField("patient_identity")
    private String patientIdentity;

    /**
     * 本人电话号码
     */
    @TableField("phone_no")
    private String phoneNo;

    /**
     * 联系电话2
     */
    @TableField("phone_no2")
    private String phoneNo2;

    /**
     * 记录创建时间
     */
    @TableField("record_datetime")
    private String recordDatetime;

    /**
     * 记录状态
     */
    @TableField("record_status")
    private Integer recordStatus;

    /**
     * 记录更新时间
     */
    @TableField("record_update_datetime")
    private LocalDateTime recordUpdateDatetime;

    /**
     * Rh血型
     */
    @TableField("rh_blood_type")
    private String rhBloodType;

    /**
     * 特殊病例标识
     */
    @TableField("tsblbs")
    private String tsblbs;

    /**
     * 就诊卡号
     */
    @TableField("visit_card_no")
    private String visitCardNo;

    /**
     * 就诊时间
     */
    @TableField("visit_datetime")
    private String visitDatetime;

    /**
     * 就诊医生姓名
     */
    @TableField("visit_doctor_name")
    private String visitDoctorName;

    /**
     * 就诊医生代码
     */
    @TableField("visit_doctor_no")
    private String visitDoctorNo;

    /**
     * 单次就诊唯一标识号
     */
    @TableId(value = "visit_sn", type = IdType.INPUT)
    private String visitSn;

    /**
     * 是否在院
     */
    @TableField("visit_status")
    private String visitStatus;

    /**
     * 就诊次数
     */
    @TableField("visit_times")
    private String visitTimes;

    /**
     * 就诊类型
     */
    @TableField("visit_type")
    private String visitType;

    /**
     * 体重
     */
    @TableField("weight")
    private String weight;

    /**
     * 微信
     */
    @TableField("weixin")
    private String weixin;

    /**
     * 数据采集时间
     */
    @TableField("yy_collection_datetime")
    private LocalDateTime yyCollectionDatetime;

    /**
     * 数据唯一标识
     */
    @TableField("yy_record_id")
    private Long yyRecordId;

    /**
     * 数据行唯一记录
     */
    @TableField("yy_record_md5")
    private String yyRecordMd5;

    /**
     * 上报状态
     */
    @TableField("yy_upload_status")
    private Integer yyUploadStatus;

    /**
     * 抽取完成时间
     */
    @TableField("yy_etl_time")
    private LocalDateTime yyEtlTime;

    /**
     * 上报完成时间
     */
    @TableField("yy_upload_time")
    private LocalDateTime yyUploadTime;

    /**
     * 上报批次号
     */
    @TableField("yy_batch_time")
    private String yyBatchTime;

    /**
     * 数据插入唯一号
     */
    @TableField("yy_record_batch_id")
    private String yyRecordBatchId;

    /**
     * 数据反填时间
     */
    @TableField("yy_backfill_time")
    private LocalDateTime yyBackfillTime;

    /**
     * 数据反填状态
     */
    @TableField("yy_backfill_status")
    private Integer yyBackfillStatus;

    /**
     * 分院编码
     */
    @TableField("branch_code")
    private String branchCode;

    /**
     * 分院名称
     */
    @TableField("branch_name")
    private String branchName;

    /**
     * 分区用业务日期
     */
    @TableField("date_for_partition")
    private LocalDateTime dateForPartition;
}
