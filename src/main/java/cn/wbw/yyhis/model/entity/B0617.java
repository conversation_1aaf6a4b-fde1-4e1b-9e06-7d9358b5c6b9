package cn.wbw.yyhis.model.entity;

import cn.wbw.yyhis.annotation.CompositeKey;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 术后首次病程
 */
@Data
@TableName("b06_1_7")
public class B0617 {

    @TableField("anesthesia_method")
    private String anesthesiaMethod;

    @TableField("content_type")
    private String contentType;

    @TableField("extend_data1")
    private String extendData1;

    @TableField("extend_data2")
    private String extendData2;

    @TableField("from_table")
    private String fromTable;

    @TableField("from_yy_record_id")
    private String fromYyRecordId;

    @TableField("hospital_code")
    @CompositeKey(order = 1, name = "hospital_code")
    private String hospitalCode;

    @TableField("hospital_name")
    private String hospitalName;

    @TableField("hospitalization_times")
    private String hospitalizationTimes;

    @TableField("inpatient_no")
    private String inpatientNo;

    @TableField("matters_need_caution")
    private String mattersNeedCaution;

    @TableField("medical_note_date")
    @CompositeKey(order = 4, name = "medical_note_date")
    private String medicalNoteDate;

    @TableField("medical_record_no")
    private String medicalRecordNo;

    @TableField("patient_id")
    private String patientId;

    @TableField("patient_id_old")
    private String patientIdOld;

    @TableField("post_operation_diagnosis")
    private String postOperationDiagnosis;

    @TableField("postoperation_treatment")
    private String postoperationTreatment;

    @TableField("record_datetime")
    private LocalDateTime recordDatetime;

    @TableId(value = "record_sn", type = IdType.INPUT)
    private String recordSn;

    @TableField("record_status")
    private Integer recordStatus;

    @TableField("record_template_name")
    private String recordTemplateName;

    @TableField("record_text")
    private String recordText;

    @TableField("record_title")
    @CompositeKey(order = 3, name = "record_title")
    private String recordTitle;

    @TableField("record_title_code")
    private String recordTitleCode;

    @TableField("record_update_datetime")
    private LocalDateTime recordUpdateDatetime;

    @TableField("signature_doctor")
    private String signatureDoctor;

    @TableField("surgery_end_datetime")
    private LocalDateTime surgeryEndDatetime;

    @TableField("surgery_method")
    private String surgeryMethod;

    @TableField("surgery_name")
    private String surgeryName;

    @TableField("surgery_process")
    private String surgeryProcess;

    @TableField("surgery_start_datetime")
    private LocalDateTime surgeryStartDatetime;

    @TableField("visit_sn")
    @CompositeKey(order = 2, name = "visit_sn")
    private String visitSn;

    @TableField("yy_collection_datetime")
    private LocalDateTime yyCollectionDatetime;

    @TableField("yy_record_id")
    private Long yyRecordId;

    @TableField("yy_record_md5")
    private String yyRecordMd5;

    @TableField("yy_upload_status")
    private Integer yyUploadStatus;

    @TableField("yy_etl_time")
    private LocalDateTime yyEtlTime;

    @TableField("yy_upload_time")
    private LocalDateTime yyUploadTime;

    @TableField("yy_batch_time")
    private String yyBatchTime;

    @TableField("yy_record_batch_id")
    private String yyRecordBatchId;

    @TableField("yy_backfill_time")
    private LocalDateTime yyBackfillTime;

    @TableField("yy_backfill_status")
    private Integer yyBackfillStatus;

    @TableField("branch_code")
    private String branchCode;

    @TableField("branch_name")
    private String branchName;

    @TableField("date_for_partition")
    private LocalDateTime dateForPartition;
} 