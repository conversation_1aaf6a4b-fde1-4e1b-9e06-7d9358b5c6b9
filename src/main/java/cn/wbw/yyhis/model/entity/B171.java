package cn.wbw.yyhis.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 常规检验记录（主）
 */
@Data
@TableName("b17_1")
public class B171 {

    @TableField("apply_datetime")
    private String applyDatetime;

    @TableField("apply_dept_code")
    private String applyDeptCode;

    @TableField("apply_dept_name")
    private String applyDeptName;

    @TableField("apply_no")
    private String applyNo;

    @TableField("exam_doc_code")
    private String examDocCode;

    @TableField("exam_doc_name")
    private String examDocName;

    @TableField("extend_data1")
    private String extendData1;

    @TableField("extend_data2")
    private String extendData2;

    @TableField("from_table")
    private String fromTable;

    @TableField("from_yy_record_id")
    private String fromYyRecordId;

    @TableField("hospital_code")
    private String hospitalCode;

    @TableField("hospital_name")
    private String hospitalName;

    @TableField("hospitalization_times")
    private String hospitalizationTimes;

    @TableField("inpatient_no")
    private String inpatientNo;

    @TableField("item_group_code")
    private String itemGroupCode;

    @TableField("item_group_name")
    private String itemGroupName;

    @TableId(value = "lab_sn", type = IdType.INPUT)
    private String labSn;

    @TableField("lab_type")
    private String labType;

    @TableField("medical_record_no")
    private String medicalRecordNo;

    @TableField("name")
    private String name;

    @TableField("order_sn")
    private String orderSn;

    @TableField("outpatient_no")
    private String outpatientNo;

    @TableField("patient_id")
    private String patientId;

    @TableField("patient_id_old")
    private String patientIdOld;

    @TableField("record_datetime")
    private String recordDatetime;

    @TableField("record_status")
    private Integer recordStatus;

    @TableField("record_update_datetime")
    private LocalDateTime recordUpdateDatetime;

    @TableField("report_datetime")
    private String reportDatetime;

    @TableField("report_dept_code")
    private String reportDeptCode;

    @TableField("report_dept_name")
    private String reportDeptName;

    @TableField("report_doc_code")
    private String reportDocCode;

    @TableField("report_doc_name")
    private String reportDocName;

    @TableField("report_no")
    private String reportNo;

    @TableField("specimen_code")
    private String specimenCode;

    @TableField("specimen_type_code")
    private String specimenTypeCode;

    @TableField("specimen_type_name")
    private String specimenTypeName;

    @TableField("test_datetime")
    private String testDatetime;

    @TableField("test_method")
    private String testMethod;

    @TableField("test_method_code")
    private String testMethodCode;

    @TableField("verify_doc_code")
    private String verifyDocCode;

    @TableField("verify_doc_name")
    private String verifyDocName;

    @TableField("visit_card_no")
    private String visitCardNo;

    @TableField("visit_sn")
    private String visitSn;

    @TableField("visit_times")
    private String visitTimes;

    @TableField("visit_type")
    private String visitType;

    @TableField("yy_collection_datetime")
    private LocalDateTime yyCollectionDatetime;

    @TableField("yy_record_id")
    private Long yyRecordId;

    @TableField("yy_record_md5")
    private String yyRecordMd5;

    @TableField("yy_upload_status")
    private Integer yyUploadStatus;

    @TableField("yy_etl_time")
    private LocalDateTime yyEtlTime;

    @TableField("yy_upload_time")
    private LocalDateTime yyUploadTime;

    @TableField("yy_batch_time")
    private String yyBatchTime;

    @TableField("yy_record_batch_id")
    private String yyRecordBatchId;

    @TableField("yy_backfill_time")
    private LocalDateTime yyBackfillTime;

    @TableField("yy_backfill_status")
    private Integer yyBackfillStatus;

    @TableField("branch_code")
    private String branchCode;

    @TableField("branch_name")
    private String branchName;

    @TableField("date_for_partition")
    private LocalDateTime dateForPartition;
} 