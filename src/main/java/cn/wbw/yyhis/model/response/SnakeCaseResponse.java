package cn.wbw.yyhis.model.response;

import cn.wbw.yyhis.config.SnakeCaseListSerializer;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 使用下划线命名的响应包装类
 * 用于将返回数据的字段名从驼峰命名转换为下划线命名
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SnakeCaseResponse<T> {
    
    @JsonSerialize(using = SnakeCaseListSerializer.class)
    private T data;
    
    public static <T> SnakeCaseResponse<T> of(T data) {
        return new SnakeCaseResponse<>(data);
    }
}
