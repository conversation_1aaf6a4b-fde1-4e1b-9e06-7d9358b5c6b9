package cn.wbw.yyhis.service;

import cn.wbw.yyhis.model.dto.B061UpsertDTO;
import cn.wbw.yyhis.model.entity.B061;
import com.baomidou.mybatisplus.extension.service.IService;

public interface B061Service extends IService<B061> {
    long countByVisitSn(String visitSn);

    /**
     * 添加入院病程记录
     */
    B061 addAdmissionRecord(B061UpsertDTO dto);

    /**
     * 更新入院病程记录
     */
    boolean updateAdmissionRecord(B061UpsertDTO dto);

    Boolean deleteByRecordSn(String recordSn);

}