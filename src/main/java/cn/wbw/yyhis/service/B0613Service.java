package cn.wbw.yyhis.service;

import cn.wbw.yyhis.model.dto.B0613UpsertDTO;
import cn.wbw.yyhis.model.entity.B0613;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 术前讨论 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
public interface B0613Service extends IService<B0613> {
    /**
     * 添加术前讨论记录
     * @param dto 数据传输对象
     * @return B0613
     */
    B0613 addPreoperativeDiscussion(B0613UpsertDTO dto);

    /**
     * 更新术前讨论记录
     * @param dto 数据传输对象
     * @return boolean
     */
    boolean updatePreoperativeDiscussion(B0613UpsertDTO dto);

    /**
     * 根据visitSn删除术前讨论记录
     * @param visitSn 就诊流水号
     * @return Boolean
     */
    Boolean deleteByVisitSn(String visitSn);
}