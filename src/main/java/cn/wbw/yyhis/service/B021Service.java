package cn.wbw.yyhis.service;

import cn.wbw.yyhis.model.dto.B021UpsertDTO;
import cn.wbw.yyhis.model.entity.B021;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 患者就诊基本信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
public interface B021Service extends IService<B021> {

    /**
     * 新增或更新患者就诊基本信息
     *
     * @param dto 患者就诊基本信息DTO
     * @return 保存后的实体
     */
    B021 upsertPatientVisitInfo(B021UpsertDTO dto);

    /**
     * 根据患者ID查询患者就诊信息
     *
     * @param patientId 患者ID
     * @return 患者就诊信息列表
     */
    java.util.List<B021> getByPatientId(String patientId);

    /**
     * 根据医院代码查询患者就诊信息
     *
     * @param hospitalCode 医院代码
     * @return 患者就诊信息列表
     */
    java.util.List<B021> getByHospitalCode(String hospitalCode);

    /**
     * 根据就诊状态查询患者就诊信息
     *
     * @param visitStatus 就诊状态
     * @return 患者就诊信息列表
     */
    java.util.List<B021> getByVisitStatus(String visitStatus);

    /**
     * 根据就诊类型查询患者就诊信息
     *
     * @param visitType 就诊类型
     * @return 患者就诊信息列表
     */
    java.util.List<B021> getByVisitType(String visitType);

    /**
     * 根据患者ID和医院代码查询患者就诊信息
     *
     * @param patientId 患者ID
     * @param hospitalCode 医院代码
     * @return 患者就诊信息列表
     */
    java.util.List<B021> getByPatientIdAndHospitalCode(String patientId, String hospitalCode);
}
