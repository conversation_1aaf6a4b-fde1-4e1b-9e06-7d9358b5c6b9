package cn.wbw.yyhis.service;

import cn.wbw.yyhis.model.dto.B101UpsertDTO;
import cn.wbw.yyhis.model.entity.B101;
import com.baomidou.mybatisplus.extension.service.IService;

public interface B101Service extends IService<B101> {

    /**
     * 添加住院医嘱记录
     * @param dto 数据传输对象
     * @return B101
     */
    B101 addMedicalOrder(B101UpsertDTO dto);

    /**
     * 更新住院医嘱记录
     * @param dto 数据传输对象
     * @return boolean
     */
    boolean updateMedicalOrder(B101UpsertDTO dto);

    Boolean delete(String orderSn);
}