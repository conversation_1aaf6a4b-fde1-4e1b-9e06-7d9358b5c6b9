package cn.wbw.yyhis.service;

import cn.wbw.yyhis.model.entity.B023;
import com.baomidou.mybatisplus.extension.service.IService;
import cn.wbw.yyhis.model.dto.B023UpsertDTO;
import cn.wbw.yyhis.model.dto.B023DTO;

import java.util.List;

/**
 * <p>
 * 患者诊断记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
public interface B023Service extends IService<B023> {

    /**
     * 根据复合主键获取详情
     * @param diagId 诊断ID号
     * @param diagSource 诊断数据来源
     * @return B023
     */
    B023 getByCompositeId(String diagId, String diagSource);

    /**
     * 根据复合主键删除
     * @param diagId 诊断ID号
     * @param diagSource 诊断数据来源
     * @return boolean
     */
    boolean removeByCompositeId(String diagId, String diagSource);

    /**
     * 根据复合主键更新
     * @param b023 实体
     * @return boolean
     */
    boolean updateByCompositeId(B023 b023);

    /**
     * 添加患者诊断记录
     * @param dto 数据传输对象
     * @return B023
     */
    B023 addPatientDiagnosis(B023UpsertDTO dto);

    /**
     * 更新患者诊断记录
     * @param dto 数据传输对象
     * @return boolean
     */
    boolean updatePatientDiagnosis(B023UpsertDTO dto);
} 