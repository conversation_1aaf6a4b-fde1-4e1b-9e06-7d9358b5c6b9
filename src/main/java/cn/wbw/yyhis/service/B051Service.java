package cn.wbw.yyhis.service;

import cn.wbw.yyhis.model.dto.B051UpsertDTO;
import cn.wbw.yyhis.model.entity.B051;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 入院记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-31
 */
public interface B051Service extends IService<B051> {
    /**
     *添加入院记录
     * @param dto 数据传输对象
     * @return B051
     */
    B051 upsertAdmissionRecord(B051UpsertDTO dto);

    Boolean deleteByVisitSn(String visitSn);
}