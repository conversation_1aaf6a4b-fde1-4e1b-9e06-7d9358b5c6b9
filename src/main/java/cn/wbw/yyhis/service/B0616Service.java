package cn.wbw.yyhis.service;

import cn.wbw.yyhis.model.dto.B0616UpsertDTO;
import cn.wbw.yyhis.model.entity.B0616;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 手术记录 服务类
 */
public interface B0616Service extends IService<B0616> {
    /**
     * 添加手术记录
     * @param dto 数据传输对象
     * @return B0616
     */
    B0616 addSurgeryRecord(B0616UpsertDTO dto);

    /**
     * 更新手术记录
     * @param dto 数据传输对象
     * @return boolean
     */
    boolean updateSurgeryRecord(B0616UpsertDTO dto);

    /**
     * 根据visitSn删除手术记录
     * @param visitSn 就诊流水号
     * @return Boolean
     */
    Boolean deleteByVisitSn(String visitSn);
}