package cn.wbw.yyhis.service;

import cn.wbw.yyhis.model.dto.B0612UpsertDTO;
import cn.wbw.yyhis.model.entity.B0612;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 会诊记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
public interface B0612Service extends IService<B0612> {
    /**
     * 添加会诊记录
     * @param dto 数据传输对象
     * @return B0612
     */
    B0612 addConsultationRecord(B0612UpsertDTO dto);

    /**
     * 更新会诊记录
     * @param dto 数据传输对象
     * @return boolean
     */
    boolean updateConsultationRecord(B0612UpsertDTO dto);

    /**
     * 根据visitSn删除会诊记录
     * @param visitSn 就诊流水号
     * @return Boolean
     */
    Boolean deleteByVisitSn(String visitSn);
}