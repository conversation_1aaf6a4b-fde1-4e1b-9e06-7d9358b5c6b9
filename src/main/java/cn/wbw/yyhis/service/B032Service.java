package cn.wbw.yyhis.service;

import cn.wbw.yyhis.model.dto.DepartmentPatientCountDTO;
import cn.wbw.yyhis.model.entity.B032;
import cn.wbw.yyhis.model.vo.PatientCardVO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 住院患者信息表(在院) 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
public interface B032Service extends IService<B032> {

    List<PatientCardVO> getPatientCards(String departmentCode, String source);

    List<DepartmentPatientCountDTO> countPatientsByDepartment(String source);

    PatientCardVO getPatientCard(String visitSn);
}