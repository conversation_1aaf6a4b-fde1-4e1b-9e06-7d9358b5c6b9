package cn.wbw.yyhis.service;

import cn.wbw.yyhis.model.dto.B0611UpsertDTO;
import cn.wbw.yyhis.model.entity.B0611;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 首次病程记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
public interface B0611Service extends IService<B0611> {
    /**
     * 添加首次病程记录
     * @param dto 数据传输对象
     * @return B0611
     */
    B0611 addFirstCourseRecord(B0611UpsertDTO dto);

    /**
     * 更新首次病程记录
     * @param dto 数据传输对象
     * @return boolean
     */
    boolean updateFirstCourseRecord(B0611UpsertDTO dto);

    Boolean deleteByVisitSn(String visitSn);
}