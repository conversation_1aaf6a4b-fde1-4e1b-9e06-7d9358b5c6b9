package cn.wbw.yyhis.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.wbw.yyhis.converter.B0617Converter;
import cn.wbw.yyhis.mapper.B0617Mapper;
import cn.wbw.yyhis.model.dto.B0617UpsertDTO;
import cn.wbw.yyhis.model.entity.B032;
import cn.wbw.yyhis.model.entity.B061;
import cn.wbw.yyhis.model.entity.B0617;
import cn.wbw.yyhis.service.B032Service;
import cn.wbw.yyhis.service.B0617Service;
import cn.wbw.yyhis.service.impl.integration.IntegrationClientService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.UUID;

@Service
@RequiredArgsConstructor
public class B0617ServiceImpl extends ServiceImpl<B0617Mapper, B0617> implements B0617Service {

    private final B032Service b032Service;
    private final IntegrationClientService integrationClientService;
    private final B0617Converter converter = B0617Converter.INSTANCE;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public B0617 addPostoperativeCourse(B0617UpsertDTO dto) {
        // 1. 根据 visit_sn 查询患者信息
        B032 b032 = b032Service.getById(dto.getVisitSn());
        if (b032 == null) {
            throw new IllegalArgumentException("未找到visit_sn为 " + dto.getVisitSn() + " 的住院患者信息");
        }

        // 2. 数据转换和填充
        B0617 b0617 = new B0617();

        // 2.1 从 B032 填充公共信息
        b0617.setVisitSn(b032.getVisitSn());
        b0617.setPatientId(b032.getPatientId());
        b0617.setInpatientNo(b032.getInpatientNo());
        b0617.setHospitalCode(b032.getHospitalCode());
        b0617.setHospitalName(b032.getHospitalName());
        b0617.setMedicalRecordNo(b032.getMedicalRecordNo());
        // ... 其他需要从B032填充的字段

        // 2.2 从 DTO 填充术后首次病程特有信息
        b0617.setPostOperationDiagnosis(dto.getPostOperationDiagnosis());
        b0617.setSurgeryName(dto.getSurgeryName());
        b0617.setAnesthesiaMethod(dto.getAnesthesiaMethod());
        b0617.setSurgeryStartDatetime(dto.getSurgeryStartDatetime());
        b0617.setSurgeryEndDatetime(dto.getSurgeryEndDatetime());
        b0617.setSurgeryProcess(dto.getSurgeryProcess());
        b0617.setPostoperationTreatment(dto.getPostoperationTreatment());
        b0617.setMattersNeedCaution(dto.getMattersNeedCaution());

        // 2.3 生成主键
        b0617.setRecordSn(UUID.randomUUID().toString().replace("-", ""));
        b0617.setRecordDatetime(LocalDateTime.now());
        b0617.setRecordUpdateDatetime(LocalDateTime.now());

        // 必填字段
        b0617.setMedicalNoteDate(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        b0617.setRecordStatus(1);
        b0617.setRecordText("");
        b0617.setRecordTitle("术后首次病程");
        b0617.setYyRecordMd5(IdUtil.fastSimpleUUID());
        // 3. 保存到数据库
        this.save(b0617);

        // 4. 重新从数据库查询最新数据用于推送
        B0617 latestB0617 = this.getById(b0617.getRecordSn());
        B061 b061 = new B061();
        b061.setHospitalCode(latestB0617.getHospitalCode());
        b061.setVisitSn(latestB0617.getVisitSn());
        b061.setRecordTitle(latestB0617.getRecordTitle());
        b061.setRecordType("术后首次病程");
        b061.setRecordDatetime(latestB0617.getRecordDatetime());
        b061.setRecordUpdateDatetime(latestB0617.getRecordUpdateDatetime());

        b061.setRecordStatus(latestB0617.getRecordStatus());

        b061.setInpatientNo(latestB0617.getInpatientNo());
        b061.setPatientId(latestB0617.getPatientId());
        b061.setMedicalRecordNo(latestB0617.getMedicalRecordNo());
        b061.setRecordSn(latestB0617.getRecordSn());
        // 以字段英文名与字段内容由冒号拼接，再由~!@#四个特殊英文字符，如下：
        // medical_note_date:字段内容~!@#surgery_name:字段内容~!@#post_operation_diagnosis:字段内容~!@#surgery_process:字段内容~!@#surgery_method:字段内容~!@#anesthesia_method:字段内容~!@#postoperation_treatment:字段内容~!@#matters_need_caution:字段内容
        String recordContent = buildRecordContent(latestB0617);
        b061.setRecordContent(recordContent);

        integrationClientService.pushHospitalDataAsync(latestB0617.getVisitSn(), B061.TABLE_NAME, b061);

        return b0617;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updatePostoperativeCourse(B0617UpsertDTO dto) {
        if (dto == null || !StringUtils.hasText(dto.getRecordSn())) {
            return false;
        }

        B0617 existingB0617 = this.getById(dto.getRecordSn());
        if (existingB0617 == null) {
            throw new IllegalArgumentException("未找到要更新的术后首次病程记录");
        }

        B0617 b0617ToUpdate = converter.toEntity(dto);
        b0617ToUpdate.setRecordUpdateDatetime(LocalDateTime.now());

        boolean updated = this.updateById(b0617ToUpdate);

        // 推送更新后的数据到第三方系统
        if (updated) {
            // 重新从数据库查询最新数据用于推送
            B0617 latestB0617 = this.getById(b0617ToUpdate.getRecordSn());
            B061 b061 = new B061();
            b061.setHospitalCode(latestB0617.getHospitalCode());
            b061.setVisitSn(latestB0617.getVisitSn());
            b061.setRecordTitle(latestB0617.getRecordTitle());
            b061.setRecordType("术后首次病程");
            b061.setRecordDatetime(latestB0617.getRecordDatetime());
            b061.setRecordUpdateDatetime(latestB0617.getRecordUpdateDatetime());

            b061.setRecordStatus(latestB0617.getRecordStatus());

            b061.setInpatientNo(latestB0617.getInpatientNo());
            b061.setPatientId(latestB0617.getPatientId());
            b061.setMedicalRecordNo(latestB0617.getMedicalRecordNo());
            b061.setRecordSn(latestB0617.getRecordSn());
            // 按照注释中的格式拼接字段内容
            String recordContent = buildRecordContent(latestB0617);
            b061.setRecordContent(recordContent);

            integrationClientService.pushHospitalDataAsync(latestB0617.getVisitSn(), B061.TABLE_NAME, b061);
        }

        return updated;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteByVisitSn(String visitSn) {
        B0617 b0617 = this.lambdaQuery().eq(B0617::getVisitSn, visitSn).one();
        if (b0617 == null) {
            throw new IllegalArgumentException("未找到要删除的术后首次病程记录");
        }
        this.lambdaUpdate().eq(B0617::getVisitSn, visitSn).remove();
        B061 b061 = new B061();
        b061.setHospitalCode(b0617.getHospitalCode());
        b061.setVisitSn(b0617.getVisitSn());
        b061.setRecordTitle(b0617.getRecordTitle());
        b061.setRecordType("术后首次病程");
        b061.setRecordDatetime(b0617.getRecordDatetime());

        integrationClientService.deleteHospitalDataByCompositeKey(visitSn, B061.TABLE_NAME, b061);
        return true;
    }

    /**
     * 构建记录内容字符串
     * 按照指定格式拼接字段：字段英文名:字段内容~!@#字段英文名:字段内容...
     *
     * @param b0617 术后首次病程实体
     * @return 拼接后的记录内容字符串
     */
    private String buildRecordContent(B0617 b0617) {
        StringBuilder recordContentBuilder = new StringBuilder();

        // 按照注释中的顺序拼接字段
        recordContentBuilder.append("medical_note_date:").append(b0617.getMedicalNoteDate() != null ? b0617.getMedicalNoteDate() : "").append("~!@#");
        recordContentBuilder.append("surgery_name:").append(b0617.getSurgeryName() != null ? b0617.getSurgeryName() : "").append("~!@#");
        recordContentBuilder.append("post_operation_diagnosis:").append(b0617.getPostOperationDiagnosis() != null ? b0617.getPostOperationDiagnosis() : "").append("~!@#");
        recordContentBuilder.append("surgery_process:").append(b0617.getSurgeryProcess() != null ? b0617.getSurgeryProcess() : "").append("~!@#");
        recordContentBuilder.append("surgery_method:").append(b0617.getSurgeryMethod() != null ? b0617.getSurgeryMethod() : "").append("~!@#");
        recordContentBuilder.append("anesthesia_method:").append(b0617.getAnesthesiaMethod() != null ? b0617.getAnesthesiaMethod() : "").append("~!@#");
        recordContentBuilder.append("postoperation_treatment:").append(b0617.getPostoperationTreatment() != null ? b0617.getPostoperationTreatment() : "").append("~!@#");
        recordContentBuilder.append("matters_need_caution:").append(b0617.getMattersNeedCaution() != null ? b0617.getMattersNeedCaution() : "");

        return recordContentBuilder.toString();
    }
}