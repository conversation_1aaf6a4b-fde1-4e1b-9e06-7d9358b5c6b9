package cn.wbw.yyhis.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.wbw.yyhis.common.ApiResult;
import cn.wbw.yyhis.converter.B071Converter;
import cn.wbw.yyhis.exception.BusinessException;
import cn.wbw.yyhis.mapper.B071Mapper;
import cn.wbw.yyhis.model.dto.B071UpsertDTO;
import cn.wbw.yyhis.model.dto.integration.HospitalDataResponse;
import cn.wbw.yyhis.model.entity.B032;
import cn.wbw.yyhis.model.entity.B071;
import cn.wbw.yyhis.service.B032Service;
import cn.wbw.yyhis.service.B071Service;
import cn.wbw.yyhis.service.impl.integration.IntegrationClientService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Slf4j
@Service
@RequiredArgsConstructor
public class B071ServiceImpl extends ServiceImpl<B071Mapper, B071> implements B071Service {

    private final B032Service b032Service;
    private final IntegrationClientService integrationClientService;

    private final B071Converter converter = B071Converter.INSTANCE;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ApiResult addDischargeRecord(B071UpsertDTO dto) {
        // 1. 根据 visit_sn 查询患者信息
        B032 b032 = b032Service.getById(dto.getVisitSn());
        if (b032 == null) {
            throw new IllegalArgumentException("未找到visit_sn为 " + dto.getVisitSn() + " 的住院患者信息");
        }

        // 2. 数据转换和填充
        B071 b071 = new B071();

        // 2.1 从 B032 填充公共信息
        b071.setVisitSn(b032.getVisitSn());
        b071.setPatientId(b032.getPatientId());
        b071.setInpatientNo(b032.getInpatientNo());
        b071.setHospitalCode(b032.getHospitalCode());
        b071.setHospitalName(b032.getHospitalName());
        b071.setMedicalRecordNo(b032.getMedicalRecordNo());
        // ... 其他需要从B032填充的字段

        // 2.2 从 DTO 填充出院记录特有信息
        b071.setAdmissionDiag(dto.getAdmissionDiag());
        b071.setDischargeDiag(dto.getDischargeDiag());
        b071.setAdmissionCondition(dto.getAdmissionCondition());
        b071.setTreatmentInfo(dto.getTreatmentInfo());
        b071.setDischargeCondition(dto.getDischargeCondition());
        b071.setDischargeOrder(dto.getDischargeOrder());

        // 2.3 生成主键
        b071.setRecordSn(UUID.randomUUID().toString().replace("-", ""));
        b071.setRecordDatetime(LocalDateTime.now());
        b071.setRecordUpdateDatetime(LocalDateTime.now());

        // 必填字段
        b071.setDischargeReason("");
        b071.setRecordStatus(1);
        b071.setRecordTitle("出院记录");
        b071.setYyRecordMd5(IdUtil.fastSimpleUUID());
        // 3. 保存到数据库
        this.save(b071);
        B071 byId = this.getById(b071.getRecordSn());
        try {
            HospitalDataResponse hospitalDataResponse = integrationClientService.pushHospitalDataNonAsync(b071.getVisitSn(), B071.TABLE_NAME, byId);
            if (!hospitalDataResponse.isSuccess()) {
                return ApiResult.error(1001,hospitalDataResponse.getResult(), "出院记录推送失败");
            }
        } catch (Exception e) {
            if (e.getMessage().contains("Read timed out")) {
                log.info("推送超时:  " + e.getMessage());
            } else {
                e.printStackTrace();
                throw new RuntimeException(e.getMessage());
            }

        }
        return ApiResult.success(byId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ApiResult updateDischargeRecord(B071UpsertDTO dto) {

        B071 existingB071 = this.getById(dto.getRecordSn());
        if (existingB071 == null) {
            throw new IllegalArgumentException("未找到要更新的出院记录");
        }

        B071 b071ToUpdate = converter.toEntity(dto);
        b071ToUpdate.setRecordUpdateDatetime(LocalDateTime.now());
        B071 byId = this.getById(b071ToUpdate.getRecordSn());
        try {
            HospitalDataResponse hospitalDataResponse = integrationClientService.pushHospitalDataNonAsync(b071ToUpdate.getVisitSn(), B071.TABLE_NAME, byId);
            if (!hospitalDataResponse.isSuccess()) {
                return ApiResult.error(1001,hospitalDataResponse.getResult(), "出院记录推送失败");
            }
        } catch (Exception e) {
            if (e.getMessage().contains("Read timed out")) {
                log.info("推送超时:  " + e.getMessage());
            } else {
                throw new RuntimeException("出院记录推送失败");
            }
        }
        // if (byId != null) {
        //     ArrayList<String> strings = new ArrayList<>();
        //     strings.add("出院记录推送失败1");
        //     strings.add("出院记录推送失败2");
        //     return ApiResult.error(1001,strings, "出院记录推送失败");
        // }

        boolean b = this.updateById(b071ToUpdate);
        return ApiResult.success(b);
    }

    @Override
    public Boolean deleteByVisitSn(String visitSn) {
        B071 one = this.lambdaQuery().eq(B071::getVisitSn, visitSn).one();
        if (one == null) {
            throw new IllegalArgumentException("未找到要删除的出院记录");
        }
        boolean remove = this.lambdaUpdate().eq(B071::getVisitSn, visitSn).remove();

        integrationClientService.deleteHospitalDataByCompositeKey(visitSn, B071.TABLE_NAME, one);
        return remove;
    }
} 