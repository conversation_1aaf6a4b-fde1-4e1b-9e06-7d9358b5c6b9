package cn.wbw.yyhis.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.wbw.yyhis.converter.B0613Converter;
import cn.wbw.yyhis.model.dto.B0613UpsertDTO;
import cn.wbw.yyhis.model.entity.B032;
import cn.wbw.yyhis.model.entity.B061;
import cn.wbw.yyhis.model.entity.B0613;
import cn.wbw.yyhis.mapper.B0613Mapper;
import cn.wbw.yyhis.service.B032Service;
import cn.wbw.yyhis.service.B0613Service;
import cn.wbw.yyhis.service.impl.integration.IntegrationClientService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.UUID;

/**
 * <p>
 * 术前讨论 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@Service
@RequiredArgsConstructor
public class B0613ServiceImpl extends ServiceImpl<B0613Mapper, B0613> implements B0613Service {

    private final B032Service b032Service;
    private final IntegrationClientService integrationClientService;
    private final B0613Converter converter = B0613Converter.INSTANCE;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public B0613 addPreoperativeDiscussion(B0613UpsertDTO dto) {
        // 1. 根据 visit_sn 查询患者信息
        B032 b032 = b032Service.getById(dto.getVisitSn());
        if (b032 == null) {
            throw new IllegalArgumentException("未找到visit_sn为 " + dto.getVisitSn() + " 的住院患者信息");
        }

        // 2. 数据转换和填充
        B0613 b0613 = new B0613();

        // 2.1 从 B032 填充公共信息
        b0613.setVisitSn(b032.getVisitSn());
        b0613.setPatientId(b032.getPatientId());
        b0613.setInpatientNo(b032.getInpatientNo());
        b0613.setHospitalCode(b032.getHospitalCode());
        b0613.setHospitalName(b032.getHospitalName());
        b0613.setMedicalRecordNo(b032.getMedicalRecordNo());
        // ... 其他需要从B032填充的字段

        // 2.2 从 DTO 填充术前讨论特有信息
        b0613.setDiscussionDatetime(dto.getDiscussionDatetime());
        b0613.setParticipantsList(dto.getParticipantsList());
        b0613.setPreoperationPreparation(dto.getPreoperationPreparation());
        b0613.setOperationIndication(dto.getOperationIndication());
        b0613.setSurgeryMethod(dto.getSurgeryMethod());
        b0613.setSurgeryPositions(dto.getSurgeryPositions());
        b0613.setOperationSetps(dto.getOperationSetps());
        b0613.setPossibleProblemsAndSolution(dto.getPossibleProblemsAndSolution());
        b0613.setDiscussionOpinion(dto.getDiscussionOpinion());
        b0613.setHostConclusion(dto.getHostConclusion());

        // 2.3 生成主键
        b0613.setRecordSn(UUID.randomUUID().toString().replace("-", ""));
        b0613.setRecordDatetime(LocalDateTime.now());
        b0613.setRecordUpdateDatetime(LocalDateTime.now());

        // 必填字段
        b0613.setMedicalNoteDate(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        b0613.setRecordStatus(1);
        b0613.setRecordText("");
        b0613.setRecordTitle("术前讨论");
        b0613.setYyRecordMd5(IdUtil.fastSimpleUUID());

        // 3. 保存到数据库
        this.save(b0613);

        // 4. 重新从数据库查询最新数据用于推送
        B0613 latestB0613 = this.getById(b0613.getRecordSn());
        B061 b061 = new B061();
        b061.setHospitalCode(latestB0613.getHospitalCode());
        b061.setVisitSn(latestB0613.getVisitSn());
        b061.setRecordTitle(latestB0613.getRecordTitle());
        b061.setRecordType("术前讨论");
        b061.setRecordDatetime(latestB0613.getRecordDatetime());
        b061.setRecordUpdateDatetime(latestB0613.getRecordUpdateDatetime());

        b061.setRecordStatus(latestB0613.getRecordStatus());

        b061.setInpatientNo(latestB0613.getInpatientNo());
        b061.setPatientId(latestB0613.getPatientId());
        b061.setMedicalRecordNo(latestB0613.getMedicalRecordNo());
        b061.setRecordSn(latestB0613.getRecordSn());

        // 以字段英文名与字段内容由冒号拼接，再由~!@#四个特殊英文字符，如下：
        // medical_note_date:字段内容~!@#discussion_datetime:字段内容~!@#participants_list:字段内容~!@#preoperation_preparation:字段内容~!@#operation_indication:字段内容~!@#surgery_method:字段内容~!@#surgery_positions:字段内容~!@#operation_setps:字段内容~!@#possible_problems_and_solution:字段内容~!@#discussion_opinion:字段内容~!@#host_conclusion:字段内容
        String recordContent = buildRecordContent(latestB0613);
        b061.setRecordContent(recordContent);

        integrationClientService.pushHospitalDataAsync(latestB0613.getVisitSn(), B061.TABLE_NAME, b061);

        return b0613;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updatePreoperativeDiscussion(B0613UpsertDTO dto) {
        if (dto == null || !StringUtils.hasText(dto.getRecordSn())) {
            return false;
        }

        B0613 existingB0613 = this.getById(dto.getRecordSn());
        if (existingB0613 == null) {
            throw new IllegalArgumentException("未找到要更新的术前讨论记录");
        }

        B0613 b0613ToUpdate = converter.dtoToEntity(dto);
        b0613ToUpdate.setRecordUpdateDatetime(LocalDateTime.now());

        boolean updated = this.updateById(b0613ToUpdate);

        // 推送更新后的数据到第三方系统
        if (updated) {
            // 重新从数据库查询最新数据用于推送
            B0613 latestB0613 = this.lambdaQuery().eq(B0613::getVisitSn, dto.getVisitSn()).one();
            B061 b061 = new B061();
            b061.setHospitalCode(latestB0613.getHospitalCode());
            b061.setVisitSn(latestB0613.getVisitSn());
            b061.setRecordTitle(latestB0613.getRecordTitle());
            b061.setRecordType("术前讨论");
            b061.setRecordDatetime(latestB0613.getRecordDatetime());
            b061.setRecordUpdateDatetime(latestB0613.getRecordUpdateDatetime());

            b061.setRecordStatus(latestB0613.getRecordStatus());

            b061.setInpatientNo(latestB0613.getInpatientNo());
            b061.setPatientId(latestB0613.getPatientId());
            b061.setMedicalRecordNo(latestB0613.getMedicalRecordNo());
            b061.setRecordSn(latestB0613.getRecordSn());
            // 按照注释中的格式拼接字段内容
            String recordContent = buildRecordContent(latestB0613);
            b061.setRecordContent(recordContent);

            integrationClientService.pushHospitalDataAsync(latestB0613.getVisitSn(), B061.TABLE_NAME, b061);
        }

        return updated;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteByVisitSn(String visitSn) {
        B0613 b0613 = this.lambdaQuery().eq(B0613::getVisitSn, visitSn).one();
        if (b0613 == null) {
            throw new IllegalArgumentException("未找到要删除的术前讨论记录");
        }
        this.lambdaUpdate().eq(B0613::getVisitSn, visitSn).remove();
        B061 b061 = new B061();
        b061.setHospitalCode(b0613.getHospitalCode());
        b061.setVisitSn(b0613.getVisitSn());
        b061.setRecordTitle(b0613.getRecordTitle());
        b061.setRecordType("术前讨论");
        b061.setRecordDatetime(b0613.getRecordDatetime());

        integrationClientService.deleteHospitalDataByCompositeKey(visitSn, B061.TABLE_NAME, b061);
        return true;
    }

    /**
     * 构建记录内容字符串
     * 按照指定格式拼接字段：字段英文名:字段内容~!@#字段英文名:字段内容...
     *
     * @param b0613 术前讨论实体
     * @return 拼接后的记录内容字符串
     */
    private String buildRecordContent(B0613 b0613) {
        StringBuilder recordContentBuilder = new StringBuilder();

        // 按照注释中的顺序拼接字段
        recordContentBuilder.append("medical_note_date:").append(b0613.getMedicalNoteDate() != null ? b0613.getMedicalNoteDate() : "").append("~!@#");
        recordContentBuilder.append("discussion_datetime:").append(b0613.getDiscussionDatetime() != null ? b0613.getDiscussionDatetime() : "").append("~!@#");
        recordContentBuilder.append("participants_list:").append(b0613.getParticipantsList() != null ? b0613.getParticipantsList() : "").append("~!@#");
        recordContentBuilder.append("preoperation_preparation:").append(b0613.getPreoperationPreparation() != null ? b0613.getPreoperationPreparation() : "").append("~!@#");
        recordContentBuilder.append("operation_indication:").append(b0613.getOperationIndication() != null ? b0613.getOperationIndication() : "").append("~!@#");
        recordContentBuilder.append("surgery_method:").append(b0613.getSurgeryMethod() != null ? b0613.getSurgeryMethod() : "").append("~!@#");
        recordContentBuilder.append("surgery_positions:").append(b0613.getSurgeryPositions() != null ? b0613.getSurgeryPositions() : "").append("~!@#");
        recordContentBuilder.append("operation_setps:").append(b0613.getOperationSetps() != null ? b0613.getOperationSetps() : "").append("~!@#");
        recordContentBuilder.append("possible_problems_and_solution:").append(b0613.getPossibleProblemsAndSolution() != null ? b0613.getPossibleProblemsAndSolution() : "").append("~!@#");
        recordContentBuilder.append("discussion_opinion:").append(b0613.getDiscussionOpinion() != null ? b0613.getDiscussionOpinion() : "").append("~!@#");
        recordContentBuilder.append("host_conclusion:").append(b0613.getHostConclusion() != null ? b0613.getHostConclusion() : "");

        return recordContentBuilder.toString();
    }
}