package cn.wbw.yyhis.service.impl;

import cn.wbw.yyhis.model.entity.SystemDictData;
import cn.wbw.yyhis.mapper.SystemDictDataMapper;
import cn.wbw.yyhis.service.SystemDictDataService;
import cn.wbw.yyhis.util.ExcelReader;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 字典数据表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@Service
public class SystemDictDataServiceImpl extends ServiceImpl<SystemDictDataMapper, SystemDictData> implements SystemDictDataService {

    @Override
    @Transactional
    public void importIcd10Data() throws IOException {
        ClassPathResource resource = new ClassPathResource("templates/ICD10编码.xlsx");
        try (InputStream inputStream = resource.getInputStream()) {
            List<SystemDictData> dictDataList = ExcelReader.readExcel(inputStream, row -> {
                // "主要编码", "附加编码", "疾病名称"
                String value = row.get(0);
                String label = row.get(2);
                if (value == null || value.trim().isEmpty() || label == null || label.trim().isEmpty()) {
                    return null;
                }
                SystemDictData dictData = new SystemDictData();
                dictData.setDictType("diagnostic_name");
                dictData.setValue(value);
                dictData.setLabel(label);
                dictData.setStatus(0);
                return dictData;
            });

            if (!dictDataList.isEmpty()) {
                this.saveBatch(dictDataList.stream().filter(java.util.Objects::nonNull).collect(Collectors.toList()));
            }
        }
    }
} 