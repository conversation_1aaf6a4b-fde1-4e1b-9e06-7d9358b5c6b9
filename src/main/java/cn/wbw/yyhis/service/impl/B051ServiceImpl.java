package cn.wbw.yyhis.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.wbw.yyhis.converter.B051Converter;
import cn.wbw.yyhis.model.dto.B051UpsertDTO;
import cn.wbw.yyhis.model.entity.B032;
import cn.wbw.yyhis.model.entity.B051;
import cn.wbw.yyhis.mapper.B051Mapper;
import cn.wbw.yyhis.service.B032Service;
import cn.wbw.yyhis.service.B051Service;
import cn.wbw.yyhis.service.impl.integration.IntegrationClientService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;

/**
 * <p>
 * 入院记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@Service
@RequiredArgsConstructor
public class B051ServiceImpl extends ServiceImpl<B051Mapper, B051> implements B051Service {

    private final B032Service b032Service;
    private final IntegrationClientService integrationClientService;
    private final B051Converter converter = B051Converter.INSTANCE;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public B051 upsertAdmissionRecord(B051UpsertDTO dto) {
        B051 b051;
        if (dto != null && StringUtils.hasText(dto.getRecordSn())) {
            B051 existingB051 = this.getById(dto.getRecordSn());
            if (existingB051 == null) {
                throw new IllegalArgumentException("未找到要更新的入院记录");
            }

            // 使用 converter 将DTO中的非空值更新到实体中
            b051 = converter.dtoToEntity(dto);
            b051.setRecordUpdateDatetime(LocalDateTime.now());

            this.updateById(b051);

        } else { // else add
            // 1. 根据 visit_sn 查询患者信息
            B032 b032 = b032Service.getById(dto.getVisitSn());
            if (b032 == null) {
                throw new IllegalArgumentException("未找到visit_sn为 " + dto.getVisitSn() + " 的住院患者信息");
            }
            // 一个visit_sn只能有一条入院记录
            if (this.count(new QueryWrapper<B051>().eq("visit_sn", dto.getVisitSn())) > 0) {
                throw new IllegalArgumentException("visit_sn为 " + dto.getVisitSn() + " 的住院患者已经存在入院记录");
            }

            // 2. 数据转换和填充
            b051 = new B051();

            // 2.1 从 B032 填充公共信息
            b051.setVisitSn(b032.getVisitSn());
            b051.setPatientId(b032.getPatientId());
            b051.setInpatientNo(b032.getInpatientNo());
            b051.setHospitalCode(b032.getHospitalCode());
            b051.setHospitalName(b032.getHospitalName());
            b051.setMedicalRecordNo(b032.getMedicalRecordNo());
            // ... 其他需要从B032填充的字段

            // 2.2 从 DTO 填充入院记录特有信息
            b051.setChiefComplaint(dto.getChiefComplaint());
            b051.setCurrentMedhistory(dto.getCurrentMedhistory());
            b051.setPastMedhistory(dto.getPastMedhistory());
            b051.setPersonalMedhistory(dto.getPersonalMedhistory());
            b051.setMenstrualHistory(dto.getMenstrualHistory());
            b051.setMarriageBirthHistory(dto.getMarriageBirthHistory());
            b051.setFamilyHistory(dto.getFamilyHistory());
            b051.setAuxiliaryExam(dto.getAuxiliaryExam());
            b051.setPhysicalExam(dto.getPhysicalExam());
            b051.setPrimaryDiagnosis(dto.getPrimaryDiagnosis());

            b051.setRecordDatetime(LocalDateTime.now());
            b051.setRecordUpdateDatetime(LocalDateTime.now());

            // 2.3 生成主键
            b051.setRecordSn(IdUtil.fastSimpleUUID());

            // 必填字段赋值
            b051.setRecordStatus(1);
            b051.setRecordTitle("入院记录");
            b051.setYyRecordMd5(IdUtil.fastSimpleUUID());

            // 3. 保存到数据库
            this.save(b051);

        }

        // 重新从数据库查询最新数据用于推送
        B051 latestB051 = this.lambdaQuery().eq(B051::getVisitSn, dto.getVisitSn()).one();
        integrationClientService.pushHospitalDataAsync(latestB051.getVisitSn(), B051.TABLE_NAME, latestB051);
        return latestB051;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteByVisitSn(String visitSn) {
        B051 one = this.lambdaQuery().eq(B051::getVisitSn, visitSn).one();
        this.lambdaUpdate().eq(B051::getVisitSn, visitSn).remove();
        integrationClientService.deleteHospitalDataByCompositeKey(visitSn, B051.TABLE_NAME, one);
        return true;
    }

} 