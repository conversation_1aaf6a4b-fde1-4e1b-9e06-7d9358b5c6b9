package cn.wbw.yyhis.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.wbw.yyhis.converter.B101Converter;
import cn.wbw.yyhis.exception.BusinessException;
import cn.wbw.yyhis.mapper.B101Mapper;
import cn.wbw.yyhis.model.dto.B101UpsertDTO;
import cn.wbw.yyhis.model.dto.integration.HospitalDataResponse;
import cn.wbw.yyhis.model.entity.B032;
import cn.wbw.yyhis.model.entity.B101;
import cn.wbw.yyhis.service.B032Service;
import cn.wbw.yyhis.service.B101Service;
import cn.wbw.yyhis.service.impl.integration.IntegrationClientService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.UUID;

@Slf4j
@Service
@RequiredArgsConstructor
public class B101ServiceImpl extends ServiceImpl<B101Mapper, B101> implements B101Service {

    private final B032Service b032Service;
    private final IntegrationClientService integrationClientService;
    private final B101Converter converter = B101Converter.INSTANCE;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public B101 addMedicalOrder(B101UpsertDTO dto) {
        // 1. 根据 visit_sn 查询患者信息
        B032 b032 = b032Service.getById(dto.getVisitSn());
        if (b032 == null) {
            throw new IllegalArgumentException("未找到visit_sn为 " + dto.getVisitSn() + " 的住院患者信息");
        }

        // 2. 数据转换和填充
        B101 b101 = converter.toEntity(dto);

        // 2.1 从 B032 填充公共信息
        b101.setVisitSn(b032.getVisitSn());
        b101.setPatientId(b032.getPatientId());
        b101.setInpatientNo(b032.getInpatientNo());
        b101.setHospitalCode(b032.getHospitalCode());
        b101.setHospitalName(b032.getHospitalName());
        b101.setMedicalRecordNo(b032.getMedicalRecordNo());
        b101.setVisitDeptCode(b032.getCurrentDeptCode());
        b101.setVisitDeptName(b032.getCurrentDeptName());

        b101.setOrderDoctorNo(b032.getAttendingPhysicianId());
        b101.setOrderDoctorName(b032.getVisitDoctorName());

        b101.setOrderDeptCode(b032.getAdmissionDeptCode());
        b101.setOrderDeptName(b032.getCurrentDeptName());

        // 2.2 生成主键和其他必要字段
        b101.setOrderSn(UUID.randomUUID().toString().replace("-", ""));
        b101.setRecordDatetime(LocalDateTime.now().toString());
        b101.setRecordUpdateDatetime(LocalDateTime.now());

        // 必填字段
        b101.setRecordStatus(1);
        b101.setOrderStatus("1");

        b101.setYyRecordMd5(IdUtil.fastSimpleUUID());
        b101.setDrugFlag("是");


        // 保存到数据库
        this.save(b101);
        B101 byId = this.getById(b101.getOrderSn());
        try {
            HospitalDataResponse hospitalDataResponse = integrationClientService.pushHospitalDataNonAsync(b101.getVisitSn(), B101.TABLE_NAME, byId);
            if (!hospitalDataResponse.isSuccess()) {
                throw new BusinessException("医嘱信息推送失败: " + hospitalDataResponse.getResult());
            }
        } catch (Exception e) {
            if (e.getMessage().contains("Read timed out")) {
                log.info("推送超时:  " + e.getMessage());
            } else {
                throw new RuntimeException("医嘱信息推送失败");
            }
        }
        return b101;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateMedicalOrder(B101UpsertDTO dto) {
        // 1. 检查记录是否存在
        B101 existingB101 = this.getById(dto.getOrderSn());
        if (existingB101 == null) {
            throw new IllegalArgumentException("未找到order_sn为 " + dto.getOrderSn() + " 的医嘱记录");
        }

        // 2. 数据转换和更新
        B101 b101 = converter.toEntity(dto);
        b101.setOrderSn(dto.getOrderSn());
        b101.setRecordUpdateDatetime(LocalDateTime.now());

        // 保留原有的系统字段
        b101.setRecordDatetime(existingB101.getRecordDatetime());
        b101.setRecordStatus(existingB101.getRecordStatus());
        b101.setYyRecordMd5(existingB101.getYyRecordMd5());

        this.updateById(b101);
        B101 byId = this.getById(b101.getOrderSn());
        try {
            HospitalDataResponse hospitalDataResponse = integrationClientService.pushHospitalDataNonAsync(b101.getVisitSn(), B101.TABLE_NAME, byId);
            if (!hospitalDataResponse.isSuccess()) {
                throw new BusinessException("医嘱信息推送失败: " + hospitalDataResponse.getResult());
            }
        } catch (Exception e) {
            if (e.getMessage().contains("Read timed out")) {
                log.info("推送超时:  " + e.getMessage());
            } else {
                throw new RuntimeException(e.getMessage());
            }
        }

        // if (byId != null) {
        //
        //     throw new BusinessException("医嘱信息推送失败");
        // }

        return true;
    }

    @Override
    public Boolean delete(String orderSn) {

        B101 one = this.getById(orderSn);
        if (one == null) {
            return true;
        }
        this.removeById(orderSn);
        integrationClientService.deleteHospitalDataByCompositeKey(one.getVisitSn(), B101.TABLE_NAME, one);
        return true;
    }
}