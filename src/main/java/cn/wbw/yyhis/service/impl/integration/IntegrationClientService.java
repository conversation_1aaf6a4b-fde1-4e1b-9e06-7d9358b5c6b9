package cn.wbw.yyhis.service.impl.integration;

import cn.hutool.core.util.StrUtil;
import cn.wbw.yyhis.constant.IntegrationConstants;
import cn.wbw.yyhis.exception.BusinessException;
import cn.wbw.yyhis.model.dto.integration.HospitalDataPacket;
import cn.wbw.yyhis.model.dto.integration.HospitalDataRequest;
import cn.wbw.yyhis.model.dto.integration.HospitalDataDeleteRequest;
import cn.wbw.yyhis.model.dto.integration.HospitalDataResponse;
import cn.wbw.yyhis.model.entity.B021;
import cn.wbw.yyhis.model.entity.B032;
import cn.wbw.yyhis.service.B021Service;
import cn.wbw.yyhis.service.B032Service;
import cn.wbw.yyhis.util.CompositeKeyUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.http.client.SimpleClientHttpRequestFactory;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;
import java.util.Base64;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Service
public class IntegrationClientService {

    private static final Logger log = LoggerFactory.getLogger(IntegrationClientService.class);

    @Value("${integration.client.baseUrl}")
    private String baseUrl;

    @Value("${integration.client.appKey}")
    private String appKey;

    @Value("${integration.client.appSecret}")
    private String appSecret;

    @Value("${integration.client.timeout.connect:1000}")
    private int connectTimeout;

    @Value("${integration.client.timeout.read:1000}")
    private int readTimeout;

    private final RestTemplate restTemplate = new RestTemplate();

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private B032Service b032Service;
    @Autowired
    private B021Service b021Service;

    @Async
    public void pushHospitalDataAsync(String visitSn, String tableCode, Object data) {
        pushHospitalData(visitSn, tableCode, Arrays.asList(data));
    }


    public HospitalDataResponse pushHospitalDataNonAsync(String visitSn, String tableCode, Object data) {
       return pushHospitalData(visitSn, tableCode, Arrays.asList(data), connectTimeout);
    }

    @Async
    public HospitalDataResponse pushHospitalData(String visitSn, String tableCode, List<?> data) {
        return pushHospitalData(visitSn, tableCode, data, null);
    }

    private HospitalDataResponse pushHospitalData(String visitSn, String tableCode, List<?> data, Integer timeoutMs) {
        B021 b021 = b021Service.lambdaQuery().eq(B021::getVisitSn, visitSn).one();
        if (b021 == null) {
            throw new BusinessException("患者就诊基本信息不存在");
        }
        B032 visit = b032Service.lambdaQuery().eq(B032::getVisitSn, visitSn).one();
        HospitalDataRequest request = new HospitalDataRequest();

        HospitalDataRequest.PatientInfo patientInfo = new HospitalDataRequest.PatientInfo();
        patientInfo.setName(b021.getName());
        patientInfo.setGender(b021.getGender());
        patientInfo.setPatient_gender(b021.getPatientGender());
        patientInfo.setNewbron_mark(b021.getNewbronMark());
        patientInfo.setBirthDate(b021.getDateOfBirth());
        // patientInfo.setMaritalStatus(b021.getMaritalStatusCode());
        // patientInfo.setPregnancyStatus();
        request.setPatientInfo(patientInfo);

        request.setPatientId(b021.getPatientId());
        request.setVisitSn(visitSn);
        // 暂时用这两个
        request.setDoctorId(visit.getAttendingPhysicianId());
        request.setDeptId(visit.getAdmissionDeptCode());
        // 在院状态
        String visitStatus = b021.getVisitStatus();
        int inHospitalStatus = "是".equals(visitStatus) ? 1 : 2;
        request.setInHospitalStatus(inHospitalStatus);
        // 就诊类型
        request.setVisit_type(b021.getVisitType());
        // 就诊次数
        String visitTimes = b021.getVisitTimes();
        if (StrUtil.isNotEmpty(visitTimes)) {
            request.setVisit_times(Integer.parseInt(visitTimes));
        }
        // 就诊时间
        request.setVisit_datetime(b021.getVisitDatetime());
        // 住院次数
        String hospitalizationTimes = visit.getHospitalizationTimes();
        if (StrUtil.isNotEmpty(hospitalizationTimes)) {
            request.setHospitalization_times(Integer.parseInt(hospitalizationTimes));
        }
        // 当前科室代码
        request.setCurrent_dept_code(visit.getCurrentDeptCode());
        // 当前科室名称
        request.setCurrent_dept_name(visit.getCurrentDeptName());
        // 管床医生代码
        request.setVisit_doctor_no(visit.getVisitDoctorNo());
        // 管床医生姓名
        request.setVisit_doctor_name(visit.getVisitDoctorName());
        // 质控场景
        // request.setQualityTarget(null);
        HospitalDataPacket dataPacket = new HospitalDataPacket();
        // 表代码
        dataPacket.setTableCode(tableCode);
        dataPacket.setData(data);
        request.setDataPacket(Arrays.asList(dataPacket));
       return pushHospitalData(request, timeoutMs);
    }


    /**
     * 推送数据到第三方系统（符合文档要求的新版本）
     *
     * @return
     */
    private HospitalDataResponse pushHospitalData(HospitalDataRequest request, Integer timeoutMs) {
        try {
            // 构造完整URL
            String fullUrl = baseUrl + IntegrationConstants.DATA_RECV_PATH;

            // 创建HTTP Headers
            HttpHeaders headers = createAuthHeaders(IntegrationConstants.DATA_RECV_PATH);

            // 创建HTTP请求
            HttpEntity<HospitalDataRequest> httpEntity = new HttpEntity<>(request, headers);

            log.info("Pushing data to external API. URL: {}, Request: {}", fullUrl, objectMapper.writeValueAsString(request));

            // 根据是否有超时参数选择RestTemplate
            RestTemplate templateToUse = restTemplate;
            if (timeoutMs != null) {
                SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
                factory.setConnectTimeout(connectTimeout);
                factory.setReadTimeout(timeoutMs);  // 读取超时使用配置的值
                templateToUse = new RestTemplate(factory);
            }

            // 发送请求
            ResponseEntity<String> response = templateToUse.exchange(
                    fullUrl,
                    HttpMethod.POST,
                    httpEntity,
                    String.class
            );

            String body = response.getBody();
            log.info("Received response from external API: {}", response.getBody());

            HospitalDataResponse hospitalDataResponse = objectMapper.readValue(body, HospitalDataResponse.class);
            return hospitalDataResponse;
        } catch (JsonProcessingException e) {
            throw new RuntimeException("json序列化异常", e);
        }
    }




    /**
     * 根据实体对象的@CompositeKey注解自动提取联合主键进行删除
     *
     * @param visitSn   就诊流水号
     * @param tableCode 表代码
     * @param entity    实体对象，会根据@CompositeKey注解提取联合主键字段
     */
    @Async
    public void deleteHospitalDataByCompositeKey(String visitSn, String tableCode, Object entity) {
        deleteHospitalDataByCompositeKey(visitSn, tableCode, Arrays.asList(entity));
    }

    /**
     * 根据实体对象列表的@CompositeKey注解自动提取联合主键进行删除
     *
     * @param visitSn   就诊流水号
     * @param tableCode 表代码
     * @param entities  实体对象列表，会根据@CompositeKey注解提取联合主键字段
     */
    @Async
    public void deleteHospitalDataByCompositeKey(String visitSn, String tableCode, List<?> entities) {
        if (entities == null || entities.isEmpty()) {
            log.warn("删除数据时实体列表为空，visitSn: {}, tableCode: {}", visitSn, tableCode);
            return;
        }

        // 检查第一个实体是否有@CompositeKey注解
        Object firstEntity = entities.get(0);
        if (firstEntity != null && !CompositeKeyUtil.hasCompositeKeyFields(firstEntity.getClass())) {
            log.warn("实体类 {} 没有@CompositeKey注解字段，请使用普通的deleteHospitalData方法",
                    firstEntity.getClass().getSimpleName());
            return;
        }

        // 提取联合主键数据
        List<Map<String, Object>> compositeKeyDataList = CompositeKeyUtil.extractCompositeKeyDataList(entities);

        if (compositeKeyDataList.isEmpty()) {
            log.warn("未能从实体列表中提取到联合主键数据，visitSn: {}, tableCode: {}", visitSn, tableCode);
            return;
        }


        // 调用原有的删除方法
        deleteHospitalData(visitSn, tableCode, compositeKeyDataList);
    }

    private void deleteHospitalData(String visitSn, String tableCode, List<?> data) {
        B021 patientInfo = b021Service.lambdaQuery().eq(B021::getVisitSn, visitSn).one();

        HospitalDataDeleteRequest request = new HospitalDataDeleteRequest();
        request.setPatientId(patientInfo.getPatientId());
        request.setVisitSn(visitSn);
        // request.setDoctorId(visit.getDoctorId());
        // request.setDeptId(visit.getDeptId());
        HospitalDataDeleteRequest.DeleteDataPacket dataPacket = new HospitalDataDeleteRequest.DeleteDataPacket();
        dataPacket.setTableCode(tableCode);
        dataPacket.setData(data);
        request.setDataPacket(Arrays.asList(dataPacket));
        deleteHospitalData(request);
    }

    /**
     * 删除数据到第三方系统（符合文档要求）
     */
    private void deleteHospitalData(HospitalDataDeleteRequest request) {
        try {
            // 构造完整URL
            String fullUrl = baseUrl + IntegrationConstants.DATA_DELETE_PATH;

            // 创建HTTP Headers（删除接口使用不同的URI）
            HttpHeaders headers = createAuthHeaders(IntegrationConstants.DATA_DELETE_PATH);

            // 创建HTTP请求
            HttpEntity<HospitalDataDeleteRequest> httpEntity = new HttpEntity<>(request, headers);

            log.info("Deleting data from external API. URL: {}, Request: {}", fullUrl, objectMapper.writeValueAsString(request));

            // 创建带超时配置的RestTemplate
            SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
            // factory.setConnectTimeout(connectTimeout);
            // factory.setReadTimeout(readTimeout);
            RestTemplate templateWithTimeout = new RestTemplate(factory);

            // 发送请求
            ResponseEntity<String> response = templateWithTimeout.exchange(
                    fullUrl,
                    HttpMethod.POST,
                    httpEntity,
                    String.class
            );

            log.info("Received response from external API: {}", response.getBody());

        } catch (JsonProcessingException e) {
            log.error("Error serializing delete request data", e);
        } catch (Exception e) {
            log.error("Error deleting data from external API", e);
        }
    }


    /**
     * 创建符合文档要求的认证Headers（默认为数据接收接口）
     */
    private HttpHeaders createAuthHeaders() {
        return createAuthHeaders(IntegrationConstants.DATA_RECV_PATH);
    }

    /**
     * 创建符合文档要求的认证Headers（指定URI）
     */
    private HttpHeaders createAuthHeaders(String requestURI) {
        HttpHeaders headers = new HttpHeaders();
        headers.set("Content-Type", IntegrationConstants.CONTENT_TYPE_JSON);
        headers.set("appKey", appKey);
        headers.set("securityLevel", IntegrationConstants.SECURITY_LEVEL_HIGH);

        // 生成时间戳
        long timestamp = System.currentTimeMillis();
        headers.set("timestamp", String.valueOf(timestamp));

        // 生成签名
        String xAuthParam = generateXAuthParam(requestURI, timestamp);
        headers.set("X-Auth-Param", xAuthParam);

        return headers;
    }

    /**
     * 生成X-Auth-Param签名（符合文档要求）
     */
    private String generateXAuthParam(String requestURI, long timestamp) {
        try {
            // Step1: 构造StringToSign
            String stringToSign = requestURI + "#" + timestamp;

            // Step2: 计算HMAC-SHA1
            Mac hmacSha1 = Mac.getInstance("HmacSHA1");
            SecretKeySpec secretKey = new SecretKeySpec(appSecret.getBytes(StandardCharsets.UTF_8), "HmacSHA1");
            hmacSha1.init(secretKey);
            byte[] hmacBytes = hmacSha1.doFinal(stringToSign.getBytes(StandardCharsets.UTF_8));

            // Step3: BASE64编码
            String sign = Base64.getEncoder().encodeToString(hmacBytes);

            // Step4: 构造X-Auth-Param
            return IntegrationConstants.AUTH_PREFIX + appKey + ":" + sign;

        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
            log.error("Error calculating HMAC-SHA1 signature", e);
            throw new RuntimeException("Could not calculate signature", e);
        }
    }
} 