package cn.wbw.yyhis.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.wbw.yyhis.converter.B0614Converter;
import cn.wbw.yyhis.mapper.B0614Mapper;
import cn.wbw.yyhis.model.dto.B0614UpsertDTO;
import cn.wbw.yyhis.model.entity.B032;
import cn.wbw.yyhis.model.entity.B061;
import cn.wbw.yyhis.model.entity.B0614;
import cn.wbw.yyhis.service.B032Service;
import cn.wbw.yyhis.service.B0614Service;
import cn.wbw.yyhis.service.impl.integration.IntegrationClientService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.UUID;

@Service
@RequiredArgsConstructor
public class B0614ServiceImpl extends ServiceImpl<B0614Mapper, B0614> implements B0614Service {

    private final B032Service b032Service;
    private final IntegrationClientService integrationClientService;
    private final B0614Converter converter = B0614Converter.INSTANCE;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public B0614 addPreoperativeSummary(B0614UpsertDTO dto) {
        // 1. 根据 visit_sn 查询患者信息
        B032 b032 = b032Service.getById(dto.getVisitSn());
        if (b032 == null) {
            throw new IllegalArgumentException("未找到visit_sn为 " + dto.getVisitSn() + " 的住院患者信息");
        }

        // 2. 数据转换和填充
        B0614 b0614 = new B0614();

        // 2.1 从 B032 填充公共信息
        b0614.setVisitSn(b032.getVisitSn());
        b0614.setPatientId(b032.getPatientId());
        b0614.setInpatientNo(b032.getInpatientNo());
        b0614.setHospitalCode(b032.getHospitalCode());
        b0614.setHospitalName(b032.getHospitalName());
        b0614.setMedicalRecordNo(b032.getMedicalRecordNo());
        // ... 其他需要从B032填充的字段

        // 2.2 从 DTO 填充术前小结特有信息
        b0614.setPreOperativeDiagnosis(dto.getPreOperativeDiagnosis());
        b0614.setDiagnosisBasis(dto.getDiagnosisBasis());
        b0614.setOperationIndication(dto.getOperationIndication());
        b0614.setPlannedSurgeryName(dto.getPlannedSurgeryName());
        b0614.setSurgeryMethod(dto.getSurgeryMethod());
        b0614.setAnesthesiaMethod(dto.getAnesthesiaMethod());
        b0614.setPossibleProblemsAndSolution(dto.getPossibleProblemsAndSolution());

        // 2.3 生成主键
        b0614.setRecordSn(UUID.randomUUID().toString().replace("-", ""));
        b0614.setRecordDatetime(LocalDateTime.now());
        b0614.setRecordUpdateDatetime(LocalDateTime.now());

        // 必填字段
        b0614.setMedicalNoteDate(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        b0614.setRecordStatus(1);
        b0614.setRecordText("");
        b0614.setRecordTitle("术前小结");
        b0614.setYyRecordMd5(IdUtil.fastSimpleUUID());
        // 3. 保存到数据库
        this.save(b0614);

        // 4. 重新从数据库查询最新数据用于推送
        B0614 latestB0614 = this.getById(b0614.getRecordSn());
        B061 b061 = new B061();
        b061.setHospitalCode(latestB0614.getHospitalCode());
        b061.setVisitSn(latestB0614.getVisitSn());
        b061.setRecordTitle(latestB0614.getRecordTitle());
        b061.setRecordType("术前小结");
        b061.setRecordDatetime(latestB0614.getRecordDatetime());
        b061.setRecordUpdateDatetime(latestB0614.getRecordUpdateDatetime());

        b061.setRecordStatus(latestB0614.getRecordStatus());

        b061.setInpatientNo(latestB0614.getInpatientNo());
        b061.setPatientId(latestB0614.getPatientId());
        b061.setMedicalRecordNo(latestB0614.getMedicalRecordNo());
        b061.setRecordSn(latestB0614.getRecordSn());

        // 以字段英文名与字段内容由冒号拼接，再由~!@#四个特殊英文字符，如下：
        // medical_note_date:字段内容~!@#pre_operative_diagnosis:字段内容~!@#diagnosis_basis:字段内容~!@#operation_indication:字段内容~!@#planned_surgery_name:字段内容~!@#surgery_method:字段内容~!@#anesthesia_method:字段内容~!@#possible_problems_and_solution:字段内容~!@#signature_doctor:字段内容
        String recordContent = buildRecordContent(latestB0614);
        b061.setRecordContent(recordContent);

        integrationClientService.pushHospitalDataAsync(latestB0614.getVisitSn(), B061.TABLE_NAME, b061);

        return b0614;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updatePreoperativeSummary(B0614UpsertDTO dto) {
        if (dto == null || !StringUtils.hasText(dto.getRecordSn())) {
            return false;
        }

        B0614 existingB0614 = this.getById(dto.getRecordSn());
        if (existingB0614 == null) {
            throw new IllegalArgumentException("未找到要更新的术前小结记录");
        }

        B0614 b0614ToUpdate = converter.toEntity(dto);
        b0614ToUpdate.setRecordUpdateDatetime(LocalDateTime.now());

        boolean updated = this.updateById(b0614ToUpdate);

        // 推送更新后的数据到第三方系统
        if (updated) {
            // 重新从数据库查询最新数据用于推送
            B0614 latestB0614 = this.getById(b0614ToUpdate.getRecordSn());
            B061 b061 = new B061();
            b061.setHospitalCode(latestB0614.getHospitalCode());
            b061.setVisitSn(latestB0614.getVisitSn());
            b061.setRecordTitle(latestB0614.getRecordTitle());
            b061.setRecordType("术前小结");
            b061.setRecordDatetime(latestB0614.getRecordDatetime());
            b061.setRecordUpdateDatetime(latestB0614.getRecordUpdateDatetime());

            b061.setRecordStatus(latestB0614.getRecordStatus());

            b061.setInpatientNo(latestB0614.getInpatientNo());
            b061.setPatientId(latestB0614.getPatientId());
            b061.setMedicalRecordNo(latestB0614.getMedicalRecordNo());
            b061.setRecordSn(latestB0614.getRecordSn());

            // 按照注释中的格式拼接字段内容
            String recordContent = buildRecordContent(latestB0614);
            b061.setRecordContent(recordContent);

            integrationClientService.pushHospitalDataAsync(latestB0614.getVisitSn(), B061.TABLE_NAME, b061);
        }

        return updated;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteByVisitSn(String visitSn) {
        B0614 b0614 = this.lambdaQuery().eq(B0614::getVisitSn, visitSn).one();
        if (b0614 == null) {
            throw new IllegalArgumentException("未找到要删除的术前小结记录");
        }
        this.lambdaUpdate().eq(B0614::getVisitSn, visitSn).remove();
        B061 b061 = new B061();
        b061.setHospitalCode(b0614.getHospitalCode());
        b061.setVisitSn(b0614.getVisitSn());
        b061.setRecordTitle(b0614.getRecordTitle());
        b061.setRecordType("术前小结");
        b061.setRecordDatetime(b0614.getRecordDatetime());

        integrationClientService.deleteHospitalDataByCompositeKey(visitSn, B061.TABLE_NAME, b061);
        return true;
    }

    /**
     * 构建记录内容字符串
     * 按照指定格式拼接字段：字段英文名:字段内容~!@#字段英文名:字段内容...
     *
     * @param b0614 术前小结实体
     * @return 拼接后的记录内容字符串
     */
    private String buildRecordContent(B0614 b0614) {
        StringBuilder recordContentBuilder = new StringBuilder();

        // 按照注释中的顺序拼接字段
        recordContentBuilder.append("medical_note_date:").append(b0614.getMedicalNoteDate() != null ? b0614.getMedicalNoteDate() : "").append("~!@#");
        recordContentBuilder.append("pre_operative_diagnosis:").append(b0614.getPreOperativeDiagnosis() != null ? b0614.getPreOperativeDiagnosis() : "").append("~!@#");
        recordContentBuilder.append("diagnosis_basis:").append(b0614.getDiagnosisBasis() != null ? b0614.getDiagnosisBasis() : "").append("~!@#");
        recordContentBuilder.append("operation_indication:").append(b0614.getOperationIndication() != null ? b0614.getOperationIndication() : "").append("~!@#");
        recordContentBuilder.append("planned_surgery_name:").append(b0614.getPlannedSurgeryName() != null ? b0614.getPlannedSurgeryName() : "").append("~!@#");
        recordContentBuilder.append("surgery_method:").append(b0614.getSurgeryMethod() != null ? b0614.getSurgeryMethod() : "").append("~!@#");
        recordContentBuilder.append("anesthesia_method:").append(b0614.getAnesthesiaMethod() != null ? b0614.getAnesthesiaMethod() : "").append("~!@#");
        recordContentBuilder.append("possible_problems_and_solution:").append(b0614.getPossibleProblemsAndSolution() != null ? b0614.getPossibleProblemsAndSolution() : "").append("~!@#");
        recordContentBuilder.append("signature_doctor:").append(b0614.getSignatureDoctor() != null ? b0614.getSignatureDoctor() : "");

        return recordContentBuilder.toString();
    }
}