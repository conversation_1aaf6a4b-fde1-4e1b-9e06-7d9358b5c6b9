package cn.wbw.yyhis.service.impl;

import cn.wbw.yyhis.model.entity.B032;
import cn.wbw.yyhis.mapper.B032Mapper;
import cn.wbw.yyhis.model.dto.DepartmentPatientCountDTO;
import cn.wbw.yyhis.model.vo.PatientCardVO;
import cn.wbw.yyhis.service.B032Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 住院患者信息表(在院) 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@Service
@RequiredArgsConstructor
public class B032ServiceImpl extends ServiceImpl<B032Mapper, B032> implements B032Service {

    private final B032Mapper b032Mapper;

    @Override
    public List<PatientCardVO> getPatientCards(String departmentCode, String source) {
        return b032Mapper.selectPatientCardsByDeptCode(departmentCode,source);
    }

    @Override
    public List<DepartmentPatientCountDTO> countPatientsByDepartment(String source) {
        return b032Mapper.countPatientsByDepartment(source);
    }

    @Override
    public PatientCardVO getPatientCard(String visitSn) {
        return b032Mapper.getPatientCard(visitSn);
    }
} 