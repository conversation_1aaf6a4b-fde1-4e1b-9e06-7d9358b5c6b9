package cn.wbw.yyhis.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.wbw.yyhis.converter.B021Converter;
import cn.wbw.yyhis.mapper.B021Mapper;
import cn.wbw.yyhis.model.dto.B021UpsertDTO;
import cn.wbw.yyhis.model.entity.B021;
import cn.wbw.yyhis.service.B021Service;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * <p>
 * 患者就诊基本信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@Service
@RequiredArgsConstructor
public class B021ServiceImpl extends ServiceImpl<B021Mapper, B021> implements B021Service {

    private final B021Converter converter = B021Converter.INSTANCE;

    @Override
    public B021 upsertPatientVisitInfo(B021UpsertDTO dto) {
        B021 b021;
        
        if (StringUtils.hasText(dto.getVisitSn())) {
            // 更新操作
            b021 = getById(dto.getVisitSn());
            if (b021 == null) {
                throw new IllegalArgumentException("未找到visitSn为 " + dto.getVisitSn() + " 的患者就诊信息");
            }
            
            // 将DTO中的非空值更新到实体中
            B021 updateEntity = converter.dtoToEntity(dto);
            updateEntity.setVisitSn(dto.getVisitSn());
            updateEntity.setRecordUpdateDatetime(LocalDateTime.now());
            
            // 保留原有的系统字段
            updateEntity.setYyRecordId(b021.getYyRecordId());
            updateEntity.setYyCollectionDatetime(b021.getYyCollectionDatetime());
            updateEntity.setYyEtlTime(b021.getYyEtlTime());
            
            updateById(updateEntity);
            b021 = updateEntity;
        } else {
            // 新增操作
            b021 = converter.dtoToEntity(dto);
            
            // 生成visitSn
            if (!StringUtils.hasText(b021.getVisitSn())) {
                b021.setVisitSn(IdUtil.getSnowflakeNextIdStr());
            }
            
            // 设置系统字段
            LocalDateTime now = LocalDateTime.now();
            b021.setRecordUpdateDatetime(now);
            b021.setYyCollectionDatetime(now);
            b021.setYyEtlTime(now);
            
            if (!StringUtils.hasText(b021.getRecordDatetime())) {
                b021.setRecordDatetime(now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            }
            
            if (b021.getRecordStatus() == null) {
                b021.setRecordStatus(1); // 默认状态为1
            }
            
            if (b021.getYyUploadStatus() == null) {
                b021.setYyUploadStatus(0); // 默认未上报
            }
            
            save(b021);
        }
        
        return b021;
    }

    @Override
    public List<B021> getByPatientId(String patientId) {
        return list(new QueryWrapper<B021>().eq("patient_id", patientId));
    }

    @Override
    public List<B021> getByHospitalCode(String hospitalCode) {
        return list(new QueryWrapper<B021>().eq("hospital_code", hospitalCode));
    }

    @Override
    public List<B021> getByVisitStatus(String visitStatus) {
        return list(new QueryWrapper<B021>().eq("visit_status", visitStatus));
    }

    @Override
    public List<B021> getByVisitType(String visitType) {
        return list(new QueryWrapper<B021>().eq("visit_type", visitType));
    }

    @Override
    public List<B021> getByPatientIdAndHospitalCode(String patientId, String hospitalCode) {
        return list(new QueryWrapper<B021>()
                .eq("patient_id", patientId)
                .eq("hospital_code", hospitalCode));
    }
}
