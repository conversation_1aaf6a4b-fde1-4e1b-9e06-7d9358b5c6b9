package cn.wbw.yyhis.service;

import cn.wbw.yyhis.model.dto.B0617UpsertDTO;
import cn.wbw.yyhis.model.entity.B0617;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 术后首次病程 服务类
 */
public interface B0617Service extends IService<B0617> {
    /**
     * 添加术后首次病程记录
     * @param dto 数据传输对象
     * @return B0617
     */
    B0617 addPostoperativeCourse(B0617UpsertDTO dto);

    /**
     * 更新术后首次病程记录
     * @param dto 数据传输对象
     * @return boolean
     */
    boolean updatePostoperativeCourse(B0617UpsertDTO dto);

    /**
     * 根据visitSn删除术后首次病程记录
     * @param visitSn 就诊流水号
     * @return Boolean
     */
    Boolean deleteByVisitSn(String visitSn);
}