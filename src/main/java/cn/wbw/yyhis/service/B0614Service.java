package cn.wbw.yyhis.service;

import cn.wbw.yyhis.model.dto.B0614UpsertDTO;
import cn.wbw.yyhis.model.entity.B0614;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 术前小结 服务类
 */
public interface B0614Service extends IService<B0614> {

    /**
     * 添加术前小结
     * @param dto 数据传输对象
     * @return B0614
     */
    B0614 addPreoperativeSummary(B0614UpsertDTO dto);

    /**
     * 更新术前小结
     * @param dto 数据传输对象
     * @return boolean
     */
    boolean updatePreoperativeSummary(B0614UpsertDTO dto);

    /**
     * 根据visitSn删除术前小结记录
     * @param visitSn 就诊流水号
     * @return Boolean
     */
    Boolean deleteByVisitSn(String visitSn);
}