package cn.wbw.yyhis.service;

import cn.wbw.yyhis.common.ApiResult;
import cn.wbw.yyhis.model.dto.B071UpsertDTO;
import cn.wbw.yyhis.model.entity.B071;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 出院记录 服务类
 */
public interface B071Service extends IService<B071> {
    /**
     * 添加出院记录
     * @param dto 数据传输对象
     * @return B071
     */
    ApiResult addDischargeRecord(B071UpsertDTO dto);

    /**
     * 更新出院记录
     *
     * @param dto 数据传输对象
     * @return boolean
     */
    ApiResult<List<?>> updateDischargeRecord(B071UpsertDTO dto);

    Boolean deleteByVisitSn(String visitSn);
}