package cn.wbw.yyhis.service;

import cn.wbw.yyhis.model.dto.TnmTStagingUpsertDTO;
import cn.wbw.yyhis.model.entity.TnmTStaging;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * TNM分期记录 服务类
 *
 * <AUTHOR>
 * @since 2024-07-07
 */
public interface TnmTStagingService extends IService<TnmTStaging> {

    /**
     * 添加TNM分期记录
     * @param dto 数据传输对象
     * @return TnmTStaging
     */
    TnmTStaging addTnmTStagingRecord(TnmTStagingUpsertDTO dto);


    Boolean deleteByVisitSn(String visitSn);
}
