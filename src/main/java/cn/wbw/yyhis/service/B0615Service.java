package cn.wbw.yyhis.service;

import cn.wbw.yyhis.model.dto.B0615UpsertDTO;
import cn.wbw.yyhis.model.entity.B0615;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 有创操作记录 服务类
 */
public interface B0615Service extends IService<B0615> {
    /**
     * 添加有创操作记录
     * @param dto 数据传输对象
     * @return B0615
     */
    B0615 addProcedureRecord(B0615UpsertDTO dto);

    /**
     * 更新有创操作记录
     * @param dto 数据传输对象
     * @return boolean
     */
    boolean updateProcedureRecord(B0615UpsertDTO dto);

    /**
     * 根据visitSn删除有创操作记录
     * @param visitSn 就诊流水号
     * @return Boolean
     */
    Boolean deleteByVisitSn(String visitSn);
}