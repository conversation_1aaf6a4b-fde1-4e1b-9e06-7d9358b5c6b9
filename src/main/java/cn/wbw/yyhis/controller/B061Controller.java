package cn.wbw.yyhis.controller;

import cn.hutool.core.util.StrUtil;
import cn.wbw.yyhis.common.ApiResult;
import cn.wbw.yyhis.converter.B061Converter;
import cn.wbw.yyhis.model.dto.B061DTO;
import cn.wbw.yyhis.model.dto.B061UpsertDTO;
import cn.wbw.yyhis.model.entity.B061;
import cn.wbw.yyhis.service.B061Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/b061")
@RequiredArgsConstructor
@Tag(name = "B061", description = "入院病程记录")
public class B061Controller {

    private final B061Service b061Service;
    private final B061Converter b061Converter = B061Converter.INSTANCE;

    @Operation(summary = "保存或更新入院病程记录")
    @PostMapping("/save")
    public B061DTO save(@RequestBody B061UpsertDTO dto) {
        if (StrUtil.isEmpty(dto.getRecordSn())) {
            // 新增
            B061 newB061 = b061Service.addAdmissionRecord(dto);
            return b061Converter.toDto(newB061);
        } else {
            // 更新
            b061Service.updateAdmissionRecord(dto);
            return b061Converter.toDto(b061Service.getById(dto.getRecordSn()));
        }
    }

    @Operation(summary = "根据ID删除入院病程记录")
    @DeleteMapping("/delete/{recordSn}")
    public Boolean delete(@PathVariable String recordSn) {
        return b061Service.deleteByRecordSn(recordSn);
    }

    @Operation(summary = "根据ID查询入院病程记录")
    @GetMapping("/detail/{recordSn}")
    public B061DTO findById(@PathVariable String recordSn) {
        B061 b061 = b061Service.getById(recordSn);
        return b061Converter.toDto(b061);
    }

    @Operation(summary = "根据就诊号visitSn查询入院病程记录")
    @GetMapping("/list")
    public List<B061DTO> findByVisitSn(@RequestParam String visitSn) {
        List<B061> list = b061Service.lambdaQuery().eq(B061::getVisitSn,visitSn)
                .orderByDesc(B061::getRecordDatetime)
                .list();
        return list.stream().map(b061Converter::toDto).toList();
    }


    @Operation(summary = "根据就诊号visitSn统计数量")
    @GetMapping("/count/{visitSn}")
    public Long countByVisitSn(@PathVariable String visitSn) {
        return b061Service.countByVisitSn(visitSn);
    }
} 