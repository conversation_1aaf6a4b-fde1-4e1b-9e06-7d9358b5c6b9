package cn.wbw.yyhis.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.wbw.yyhis.converter.B0614Converter;
import cn.wbw.yyhis.model.dto.B0614DTO;
import cn.wbw.yyhis.model.dto.B0614UpsertDTO;
import cn.wbw.yyhis.model.entity.B023;
import cn.wbw.yyhis.model.entity.B0614;
import cn.wbw.yyhis.service.B023Service;
import cn.wbw.yyhis.service.B0614Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/b0614")
@RequiredArgsConstructor
@Tag(name = "B0614", description = "术前小结")
public class B0614Controller {

    private final B0614Service b0614Service;
    private final B023Service b023Service;
    private final B0614Converter b0614Converter = B0614Converter.INSTANCE;

    @PostMapping("/save")
    public B0614DTO save(@RequestBody B0614UpsertDTO dto) {
        if (!StringUtils.hasText(dto.getRecordSn())) {
            B0614 newB0614 = b0614Service.addPreoperativeSummary(dto);
            return b0614Converter.toDto(newB0614);
        } else {
            b0614Service.updatePreoperativeSummary(dto);
            return b0614Converter.toDto(b0614Service.getById(dto.getRecordSn()));
        }
    }

    @DeleteMapping("/delete/{visitSn}")
    public Boolean delete(@PathVariable String visitSn) {
        return b0614Service.deleteByVisitSn(visitSn);
    }

    @GetMapping("/detail/{visitSn}")
    public B0614DTO detail(@PathVariable String visitSn) {
        B0614 one = b0614Service.lambdaQuery().eq(B0614::getVisitSn, visitSn).one();
        if (one != null) {
            List<B023> list = b023Service.lambdaQuery()
                    .eq(B023::getVisitSn, visitSn)
                    .eq(B023::getDiagType, "术前诊断").select(B023::getDiagName).list();
            String diagnosis ="";
            if (CollectionUtil.isNotEmpty(list)) {
                diagnosis = list.stream().map(B023::getDiagName).collect(Collectors.joining("，"));
            }
            one.setPreOperativeDiagnosis(diagnosis);
        }
        return b0614Converter.toDto(one);
    }

    @GetMapping("/all")
    public List<B0614DTO> findAll() {
        List<B0614> list = b0614Service.list();
        return list.stream().map(b0614Converter::toDto).toList();
    }

    @GetMapping("/page")
    public IPage<B0614DTO> findPage(@RequestParam(defaultValue = "1") Integer pageNum,
                                               @RequestParam(defaultValue = "10") Integer pageSize) {
        Page<B0614> page = new Page<>(pageNum, pageSize);
        Page<B0614> b0614Page = b0614Service.page(page);
        IPage<B0614DTO> dtoPage = b0614Page.convert(b0614Converter::toDto);
        return dtoPage;
    }
} 