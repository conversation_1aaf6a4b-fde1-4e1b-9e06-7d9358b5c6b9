package cn.wbw.yyhis.controller;

import cn.wbw.yyhis.converter.TnmTStagingConverter;
import cn.wbw.yyhis.model.dto.TnmTStagingDTO;
import cn.wbw.yyhis.model.dto.TnmTStagingUpsertDTO;
import cn.wbw.yyhis.model.entity.TnmTStaging;
import cn.wbw.yyhis.service.TnmTStagingService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * TNM分期记录 前端控制器
 *
 * <AUTHOR>
 * @since 2024-07-07
 */
@RestController
@RequestMapping("/tnm-t-staging")
@RequiredArgsConstructor
public class TnmTStagingController {

    private final TnmTStagingService tnmTStagingService;
    private final TnmTStagingConverter converter = TnmTStagingConverter.INSTANCE;

    /**
     * 保存或更新TNM分期记录
     *
     * @param dto TNM分期记录数据
     * @return TnmTStagingDTO
     */
    @PostMapping("/save")
    public TnmTStagingDTO save(@RequestBody TnmTStagingUpsertDTO dto) {
        TnmTStaging newRecord = tnmTStagingService.addTnmTStagingRecord(dto);
        return converter.entityToDto(newRecord);
    }

    /**
     * 根据visitSn删除TNM分期记录
     *
     * @param visitSn 就诊流水号
     * @return Boolean
     */
    @DeleteMapping("/delete/{visitSn}")
    public Boolean delete(@PathVariable String visitSn) {
        return tnmTStagingService.deleteByVisitSn(visitSn);
    }

    /**
     * 获取所有TNM分期记录
     *
     * @return List<TnmTStagingDTO>
     */
    @GetMapping("/all")
    public List<TnmTStagingDTO> findAll() {
        return converter.entityListToDtoList(tnmTStagingService.list());
    }

    /**
     * 根据visitSn获取TNM分期记录详情
     *
     * @param visitSn 就诊流水号
     * @return TnmTStagingDTO
     */
    @GetMapping("/detail/{visitSn}")
    public TnmTStagingDTO detail(@PathVariable String visitSn) {
        return converter.entityToDto(tnmTStagingService.lambdaQuery().eq(TnmTStaging::getVisitSn, visitSn).one());
    }

    /**
     * 分页查询TNM分期记录
     *
     * @param pageNum  页码
     * @param pageSize 每页大小
     * @return Page<TnmTStagingDTO>
     */
    @GetMapping("/page")
    public Page<TnmTStagingDTO> findPage(@RequestParam(defaultValue = "1") Integer pageNum,
                                         @RequestParam(defaultValue = "10") Integer pageSize) {
        Page<TnmTStaging> page = new Page<>(pageNum, pageSize);
        Page<TnmTStaging> entityPage = tnmTStagingService.page(page);
        Page<TnmTStagingDTO> dtoPage = new Page<>(entityPage.getCurrent(), entityPage.getSize(), entityPage.getTotal());
        dtoPage.setRecords(converter.entityListToDtoList(entityPage.getRecords()));
        return dtoPage;
    }
}
