package cn.wbw.yyhis.controller;

import cn.wbw.yyhis.converter.B0615Converter;
import cn.wbw.yyhis.model.dto.B0615DTO;
import cn.wbw.yyhis.model.dto.B0615UpsertDTO;
import cn.wbw.yyhis.model.entity.B0615;
import cn.wbw.yyhis.service.B0615Service;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/b0615")
@RequiredArgsConstructor
@Tag(name = "B0615", description = "有创操作记录")
public class B0615Controller {

    private final B0615Service b0615Service;
    private final B0615Converter converter = B0615Converter.INSTANCE;

    @Operation(summary = "新增有创操作记录")
    @PostMapping("/save")
    public B0615DTO save(@RequestBody B0615UpsertDTO dto) {
        if (!StringUtils.hasText(dto.getRecordSn())) {
            B0615 newB0615 = b0615Service.addProcedureRecord(dto);
            return converter.toDto(newB0615);
        } else {
            b0615Service.updateProcedureRecord(dto);
            return converter.toDto(b0615Service.getById(dto.getRecordSn()));
        }
    }

    @Operation(summary = "根据visitSn删除有创操作记录")
    @DeleteMapping("/delete/{visitSn}")
    public Boolean delete(@PathVariable String visitSn) {
        return b0615Service.deleteByVisitSn(visitSn);
    }

    @Operation(summary = "查询所有有创操作记录")
    @GetMapping("/all")
    public List<B0615DTO> findAll() {
        List<B0615> list = b0615Service.list();
        return list.stream().map(converter::toDto).collect(Collectors.toList());
    }

    @Operation(summary = "根据visitSn查询有创操作记录")
    @GetMapping("/detail/{visitSn}")
    public B0615DTO detail(@PathVariable String visitSn) {
        B0615 b0615 = b0615Service.lambdaQuery().eq(B0615::getVisitSn, visitSn).one();
        return converter.toDto(b0615);
    }

    @Operation(summary = "分页查询有创操作记录")
    @GetMapping("/page")
    public Page<B0615DTO> findPage(@RequestParam(defaultValue = "1") Integer pageNum,
                                   @RequestParam(defaultValue = "10") Integer pageSize) {
        Page<B0615> page = new Page<>(pageNum, pageSize);
        Page<B0615> entityPage = b0615Service.page(page);
        Page<B0615DTO> dtoPage = new Page<>(entityPage.getCurrent(), entityPage.getSize(), entityPage.getTotal());
        dtoPage.setRecords(entityPage.getRecords().stream().map(converter::toDto).collect(Collectors.toList()));
        return dtoPage;
    }
} 