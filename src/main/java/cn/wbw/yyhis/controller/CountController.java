package cn.wbw.yyhis.controller;

import cn.wbw.yyhis.converter.B023Converter;
import cn.wbw.yyhis.model.dto.B023DTO;
import cn.wbw.yyhis.model.dto.B023UpsertDTO;
import cn.wbw.yyhis.model.dto.CountDTO;
import cn.wbw.yyhis.model.entity.*;
import cn.wbw.yyhis.service.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 患者诊断记录 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@RestController
@RequestMapping("/count")
@RequiredArgsConstructor
public class CountController {

    private final B061Service b061Service;
    private final B161Service b161service;
    private final B162Service b162service;
    private final B163Service b163service;
    private final B171Service b171service;

    @GetMapping
    public CountDTO count(@RequestParam String visitSn) {
        CountDTO countDTO = new CountDTO();

        Long count = b061Service.lambdaQuery().eq(B061::getVisitSn, visitSn).count();
        countDTO.setB061Count(count);
        count = b161service.lambdaQuery().eq(B161::getVisitSn, visitSn).count();
        countDTO.setB161Count(count);
        count = b162service.lambdaQuery().eq(B162::getVisitSn, visitSn).count();
        countDTO.setB162Count(count);
        count = b163service.lambdaQuery().eq(B163::getVisitSn, visitSn).count();
        countDTO.setB163Count(count);
        count = b171service.lambdaQuery().eq(B171::getVisitSn, visitSn).count();
        countDTO.setB171Count(count);

        return countDTO;
    }

} 