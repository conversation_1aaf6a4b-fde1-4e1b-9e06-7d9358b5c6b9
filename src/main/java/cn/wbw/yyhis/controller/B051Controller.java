package cn.wbw.yyhis.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.wbw.yyhis.converter.B051Converter;
import cn.wbw.yyhis.model.dto.B051DTO;
import cn.wbw.yyhis.model.dto.B051UpsertDTO;
import cn.wbw.yyhis.model.entity.B023;
import cn.wbw.yyhis.model.entity.B051;
import cn.wbw.yyhis.service.B023Service;
import cn.wbw.yyhis.service.B051Service;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 入院记录 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@RestController
@RequestMapping("/b051")
@RequiredArgsConstructor
public class B051Controller {

    private final B051Service b051Service;
    private final B023Service b023Service;
    private final B051Converter converter = B051Converter.INSTANCE;

    @PostMapping("/save")
    public B051DTO save(@RequestBody B051UpsertDTO dto) {
        B051 savedB051 = b051Service.upsertAdmissionRecord(dto);
        return converter.entityToDto(savedB051);
    }

    @DeleteMapping("/delete/{visitSn}")
    public Boolean delete(@PathVariable String visitSn) {
        return  b051Service.deleteByVisitSn(visitSn);
    }


    @GetMapping("/detail/{visitSn}")
    public B051DTO findById(@PathVariable String visitSn) {
        B051 one = b051Service.lambdaQuery().eq(B051::getVisitSn, visitSn).one();
        if (one != null) {
            List<B023> list = b023Service.lambdaQuery()
                    .eq(B023::getVisitSn, visitSn)
                    .eq(B023::getDiagType, "入院诊断").select(B023::getDiagName).list();
            String primaryDiagnosis ="";
            if (CollectionUtil.isNotEmpty(list)) {
                primaryDiagnosis = list.stream().map(B023::getDiagName).collect(Collectors.joining("，"));
            }
            one.setPrimaryDiagnosis(primaryDiagnosis);
        }
        return converter.entityToDto(one);
    }


} 