package cn.wbw.yyhis.controller;

import cn.wbw.yyhis.converter.B0612Converter;
import cn.wbw.yyhis.model.dto.B0612DTO;
import cn.wbw.yyhis.model.dto.B0612UpsertDTO;
import cn.wbw.yyhis.model.entity.B0612;
import cn.wbw.yyhis.service.B0612Service;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 会诊记录 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@RestController
@RequestMapping("/b0612")
@RequiredArgsConstructor
public class B0612Controller {

    private final B0612Service b0612Service;
    private final B0612Converter converter = B0612Converter.INSTANCE;

    @PostMapping("/save")
    public B0612DTO save(@RequestBody B0612UpsertDTO dto) {
        if (!StringUtils.hasText(dto.getRecordSn())) {
            B0612 newB0612 = b0612Service.addConsultationRecord(dto);
            return converter.entityToDto(newB0612);
        } else {
            b0612Service.updateConsultationRecord(dto);
            return converter.entityToDto(b0612Service.getById(dto.getRecordSn()));
        }
    }

    @DeleteMapping("/delete/{visitSn}")
    public Boolean delete(@PathVariable String visitSn) {
        return b0612Service.deleteByVisitSn(visitSn);
    }

    @GetMapping("/all")
    public List<B0612DTO> findAll() {
        return converter.entityListToDtoList(b0612Service.list());
    }

    @GetMapping("/detail/{visitSn}")
    public B0612DTO detail(@PathVariable String visitSn) {
        return converter.entityToDto(b0612Service.lambdaQuery().eq(B0612::getVisitSn, visitSn).one());
    }

    @GetMapping("/page")
    public Page<B0612DTO> findPage(@RequestParam(defaultValue = "1") Integer pageNum,
                                   @RequestParam(defaultValue = "10") Integer pageSize) {
        Page<B0612> page = new Page<>(pageNum, pageSize);
        Page<B0612> entityPage = b0612Service.page(page);
        Page<B0612DTO> dtoPage = new Page<>(entityPage.getCurrent(), entityPage.getSize(), entityPage.getTotal());
        dtoPage.setRecords(converter.entityListToDtoList(entityPage.getRecords()));
        return dtoPage;
    }
} 