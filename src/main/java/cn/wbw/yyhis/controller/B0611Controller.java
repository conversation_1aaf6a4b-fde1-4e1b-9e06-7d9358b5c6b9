package cn.wbw.yyhis.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.wbw.yyhis.converter.B0611Converter;
import cn.wbw.yyhis.model.dto.B0611DTO;
import cn.wbw.yyhis.model.dto.B0611UpsertDTO;
import cn.wbw.yyhis.model.entity.B023;
import cn.wbw.yyhis.model.entity.B0611;
import cn.wbw.yyhis.service.B023Service;
import cn.wbw.yyhis.service.B0611Service;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 首次病程记录 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@RestController
@RequestMapping("/b0611")
@RequiredArgsConstructor
public class B0611Controller {

    private final B0611Service b0611Service;
    private final B023Service b023Service;
    private final B0611Converter converter = B0611Converter.INSTANCE;

    @PostMapping("/save")
    public B0611DTO save(@RequestBody B0611UpsertDTO dto) {
        if (!StringUtils.hasText(dto.getRecordSn())) {
            B0611 newB0611 = b0611Service.addFirstCourseRecord(dto);
            return converter.entityToDto(newB0611);
        } else {
            b0611Service.updateFirstCourseRecord(dto);
            return converter.entityToDto(b0611Service.getById(dto.getRecordSn()));
        }
    }

    @DeleteMapping("/delete/{visitSn}")
    public Boolean delete(@PathVariable String visitSn) {
       return b0611Service.deleteByVisitSn(visitSn);
    }

    @GetMapping("/detail/{visitSn}")
    public B0611DTO detail(@PathVariable String visitSn) {
        B0611 one = b0611Service.lambdaQuery().eq(B0611::getVisitSn, visitSn).one();
        if (one != null) {
            List<B023> list = b023Service.lambdaQuery()
                    .eq(B023::getVisitSn, visitSn)
                    .eq(B023::getDiagType, "入院诊断").select(B023::getDiagName).list();
            String primaryDiagnosis ="";
            if (CollectionUtil.isNotEmpty(list)) {
                primaryDiagnosis = list.stream().map(B023::getDiagName).collect(Collectors.joining("，"));
            }
            one.setPrimaryDiagnosis(primaryDiagnosis);
        }
        return converter.entityToDto(one);
    }


} 