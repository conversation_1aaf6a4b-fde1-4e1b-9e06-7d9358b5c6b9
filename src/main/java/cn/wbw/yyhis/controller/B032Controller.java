package cn.wbw.yyhis.controller;

import cn.wbw.yyhis.converter.B032Converter;
import cn.wbw.yyhis.model.dto.B032DTO;
import cn.wbw.yyhis.model.dto.DepartmentPatientCountDTO;
import cn.wbw.yyhis.model.entity.B032;
import cn.wbw.yyhis.model.vo.PatientCardVO;
import cn.wbw.yyhis.service.B032Service;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 住院患者信息表(在院) 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@RestController
@RequestMapping("/b032")
@RequiredArgsConstructor
public class B032Controller {

    private final B032Service b032Service;
    private final B032Converter b032Converter = B032Converter.INSTANCE;

    @PostMapping("/add")
    public Boolean add(@RequestBody B032DTO b032DTO) {
        B032 b032 = b032Converter.dtoToEntity(b032DTO);
        return b032Service.save(b032);
    }

    @DeleteMapping("/delete/{id}")
    public Boolean delete(@PathVariable("id") String id) {
        return b032Service.removeById(id);
    }

    @PutMapping("/update/{id}")
    public Boolean update(@PathVariable("id") String id, @RequestBody B032DTO b032DTO) {
        b032DTO.setVisitSn(id);
        B032 b032 = b032Converter.dtoToEntity(b032DTO);
        return b032Service.updateById(b032);
    }

    @GetMapping("/all")
    public List<B032DTO> findAll() {
        List<B032> list = b032Service.list();
        return b032Converter.entityListToDtoList(list);
    }

    @GetMapping("/patient-cards/getByVisitSn")
    public PatientCardVO getPatientCard(@RequestParam("visitSn") String visitSn) {
        return b032Service.getPatientCard(visitSn);
    }

    @GetMapping("/patient-cards")
    public List<PatientCardVO> getPatientCards(@RequestParam("departmentCode") String departmentCode,@RequestParam String source) {
        return b032Service.getPatientCards(departmentCode,source);
    }

    @GetMapping("/stats/by-department")
    public List<DepartmentPatientCountDTO> countPatientsByDepartment(@RequestParam String source ) {
        return b032Service.countPatientsByDepartment(source);
    }

    @GetMapping("/page")
    public Page<B032DTO> findPage(@RequestParam(defaultValue = "1") Integer pageNum,
                                  @RequestParam(defaultValue = "10") Integer pageSize) {
        Page<B032> page = new Page<>(pageNum, pageSize);
        Page<B032> b032Page = b032Service.page(page);
        Page<B032DTO> dtoPage = new Page<>(b032Page.getCurrent(), b032Page.getSize(), b032Page.getTotal());
        dtoPage.setRecords(b032Converter.entityListToDtoList(b032Page.getRecords()));
        return dtoPage;
    }
} 