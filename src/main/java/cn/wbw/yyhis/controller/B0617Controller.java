package cn.wbw.yyhis.controller;

import cn.wbw.yyhis.converter.B0617Converter;
import cn.wbw.yyhis.model.dto.B0617DTO;
import cn.wbw.yyhis.model.dto.B0617UpsertDTO;
import cn.wbw.yyhis.model.entity.B0617;
import cn.wbw.yyhis.service.B0617Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/b0617")
@RequiredArgsConstructor
@Tag(name = "B0617", description = "术后首次病程")
public class B0617Controller {

    private final B0617Service b0617Service;
    private final B0617Converter b0617Converter = B0617Converter.INSTANCE;

    @PostMapping("/save")
    public B0617DTO save(@RequestBody B0617UpsertDTO dto) {
        if (!StringUtils.hasText(dto.getRecordSn())) {
            B0617 newB0617 = b0617Service.addPostoperativeCourse(dto);
            return b0617Converter.toDto(newB0617);
        } else {
            b0617Service.updatePostoperativeCourse(dto);
            return b0617Converter.toDto(b0617Service.getById(dto.getRecordSn()));
        }
    }

    @DeleteMapping("/delete/{visitSn}")
    public Boolean delete(@PathVariable String visitSn) {
        return b0617Service.deleteByVisitSn(visitSn);
    }

    @GetMapping("/detail/{visitSn}")
    public B0617DTO detail(@PathVariable String visitSn) {
        return b0617Converter.toDto(b0617Service.lambdaQuery().eq(B0617::getVisitSn, visitSn).one());
    }

    @GetMapping("/all")
    public List<B0617DTO> findAll() {
        List<B0617> list = b0617Service.list();
        return list.stream().map(b0617Converter::toDto).toList();
    }

    @GetMapping("/page")
    public IPage<B0617DTO> findPage(@RequestParam(defaultValue = "1") Integer pageNum,
                                               @RequestParam(defaultValue = "10") Integer pageSize) {
        Page<B0617> page = new Page<>(pageNum, pageSize);
        Page<B0617> b0617Page = b0617Service.page(page);
        IPage<B0617DTO> dtoPage = b0617Page.convert(b0617Converter::toDto);
        return dtoPage;
    }
} 