package cn.wbw.yyhis.controller;

import cn.wbw.yyhis.model.dto.B171DTO;
import cn.wbw.yyhis.model.entity.B171;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import cn.wbw.yyhis.common.ApiResult;
import cn.wbw.yyhis.model.dto.B161DTO;
import cn.wbw.yyhis.model.entity.B161;
import cn.wbw.yyhis.service.B161Service;
import cn.wbw.yyhis.converter.B161Converter;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

import java.util.List;

/**
 * <p>
 * 常规检查记录 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-26
 */
@Tag(name = "常规检查记录")
@RestController
@RequestMapping("/b161")
public class B161Controller {

    @Autowired
    private B161Service b161Service;

    @Autowired
    private B161Converter b161Converter;



    @Operation(summary = "修改")
    @PutMapping("/update")
    public Boolean update(@RequestBody B161DTO b161DTO) {
        return b161Service.updateDto(b161DTO);
    }

    @Operation(summary = "删除")
    @DeleteMapping("/delete/{id}")
    public Boolean delete(@PathVariable("id") String id) {
        return b161Service.deleteByReportNo(id);
    }


    @Operation(summary = "查询所有常规检验记录")
    @GetMapping("/list/{visitSn}")
    public List<B161DTO> list(@PathVariable String visitSn) {
        List<B161> list = b161Service.lambdaQuery().eq(B161::getVisitSn,visitSn).list();
        return list.stream().map(b161Converter::entityToDto).toList();
    }
} 