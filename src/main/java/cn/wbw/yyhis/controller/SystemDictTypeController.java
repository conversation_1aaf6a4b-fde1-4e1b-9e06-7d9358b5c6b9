package cn.wbw.yyhis.controller;

import cn.wbw.yyhis.converter.SystemDictTypeConverter;
import cn.wbw.yyhis.model.dto.SystemDictTypeDTO;
import cn.wbw.yyhis.model.entity.SystemDictType;
import cn.wbw.yyhis.service.SystemDictTypeService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 字典类型表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@RestController
@RequestMapping("/system-dict-type")
@RequiredArgsConstructor
public class SystemDictTypeController {

    private final SystemDictTypeService systemDictTypeService;
    private final SystemDictTypeConverter converter = SystemDictTypeConverter.INSTANCE;

    @PostMapping("/add")
    public Boolean add(@RequestBody SystemDictTypeDTO dto) {
        return systemDictTypeService.save(converter.dtoToEntity(dto));
    }

    @DeleteMapping("/delete/{id}")
    public Boolean delete(@PathVariable Long id) {
        return systemDictTypeService.removeById(id);
    }

    @PutMapping("/update/{id}")
    public Boolean update(@PathVariable Long id, @RequestBody SystemDictTypeDTO dto) {
        dto.setId(id);
        return systemDictTypeService.updateById(converter.dtoToEntity(dto));
    }

    @GetMapping("/all")
    public List<SystemDictTypeDTO> findAll() {
        return converter.entityListToDtoList(systemDictTypeService.list());
    }

    @GetMapping("/detail/{id}")
    public SystemDictTypeDTO findById(@PathVariable Long id) {
        return converter.entityToDto(systemDictTypeService.getById(id));
    }

    @GetMapping("/page")
    public Page<SystemDictTypeDTO> findPage(@RequestParam(defaultValue = "1") Integer pageNum,
                                            @RequestParam(defaultValue = "10") Integer pageSize) {
        Page<SystemDictType> page = new Page<>(pageNum, pageSize);
        Page<SystemDictType> entityPage = systemDictTypeService.page(page);
        Page<SystemDictTypeDTO> dtoPage = new Page<>(entityPage.getCurrent(), entityPage.getSize(), entityPage.getTotal());
        dtoPage.setRecords(converter.entityListToDtoList(entityPage.getRecords()));
        return dtoPage;
    }
} 