package cn.wbw.yyhis.controller;

import cn.wbw.yyhis.common.ApiResult;
import cn.wbw.yyhis.converter.B101Converter;
import cn.wbw.yyhis.exception.BusinessException;
import cn.wbw.yyhis.model.dto.B101DTO;
import cn.wbw.yyhis.model.dto.B101UpsertDTO;
import cn.wbw.yyhis.model.entity.B101;
import cn.wbw.yyhis.service.B101Service;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 住院医嘱记录 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@RestController
@RequestMapping("/b101")
@RequiredArgsConstructor
@Tag(name = "B101", description = "住院医嘱记录")
public class B101Controller {

    private final B101Service b101Service;
    private final B101Converter converter = B101Converter.INSTANCE;

    @Operation(summary = "新增住院医嘱记录")
    @PostMapping("/add")
    public ApiResult add(@RequestBody B101UpsertDTO dto) {
        try {
            B101 newB101 = b101Service.addMedicalOrder(dto);
            B101DTO b101DTO = converter.entityToDto(newB101);
            return ApiResult.success(b101DTO);
        } catch (BusinessException e) {
            return ApiResult.error(1002,e.getMessage());
        }
    }

    @Operation(summary = "根据ID删除住院医嘱记录")
    @DeleteMapping("/delete/{orderSn}")
    public Boolean delete(@PathVariable String orderSn) {
      return   b101Service.delete(orderSn);

    }

    @Operation(summary = "更新住院医嘱记录")
    @PutMapping("/update/{orderSn}")
    public ApiResult update(@PathVariable String orderSn, @RequestBody B101UpsertDTO dto) {

        try {
            dto.setOrderSn(orderSn);
            boolean b = b101Service.updateMedicalOrder(dto);
            return ApiResult.success(b);
        } catch (BusinessException e) {
            return ApiResult.error(1002,e.getMessage());
        }

    }

    @Operation(summary = "根据就诊号查询住院医嘱记录列表")
    @GetMapping("/list/{visitSn}")
    public List<B101DTO> listByVisitSn(@PathVariable String visitSn) {
        List<B101> list = b101Service.lambdaQuery().eq(B101::getVisitSn, visitSn)
                .orderByDesc(B101::getRecordDatetime)
                .list();
        return converter.entityListToDtoList(list);
    }

    @Operation(summary = "根据ID查询住院医嘱记录")
    @GetMapping("/detail/{orderSn}")
    public B101DTO findById(@PathVariable String orderSn) {
        return converter.entityToDto(b101Service.getById(orderSn));
    }

    @Operation(summary = "分页查询住院医嘱记录")
    @GetMapping("/page")
    public Page<B101DTO> findPage(@RequestParam(defaultValue = "1") Integer pageNum,
                                  @RequestParam(defaultValue = "10") Integer pageSize) {
        Page<B101> page = new Page<>(pageNum, pageSize);
        Page<B101> entityPage = b101Service.page(page);
        Page<B101DTO> dtoPage = new Page<>(entityPage.getCurrent(), entityPage.getSize(), entityPage.getTotal());
        dtoPage.setRecords(converter.entityListToDtoList(entityPage.getRecords()));
        return dtoPage;
    }
} 