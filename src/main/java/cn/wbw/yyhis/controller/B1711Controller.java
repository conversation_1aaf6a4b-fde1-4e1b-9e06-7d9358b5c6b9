package cn.wbw.yyhis.controller;

import cn.wbw.yyhis.model.dto.B171DTO;
import cn.wbw.yyhis.model.entity.B171;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import cn.wbw.yyhis.common.ApiResult;
import cn.wbw.yyhis.model.dto.B1711DTO;
import cn.wbw.yyhis.model.entity.B1711;
import cn.wbw.yyhis.service.B1711Service;
import cn.wbw.yyhis.converter.B1711Converter;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

import java.util.List;

/**
 * <p>
 * 常规检验记录（明细） 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-26
 */
@Tag(name = "常规检验记录（明细）")
@RestController
@RequestMapping("/b1711")
public class B1711Controller {

    @Autowired
    private B1711Service b1711Service;

    @Autowired
    private B1711Converter b1711Converter;


    @Operation(summary = "查询常规检验记录（明细）")
    @GetMapping("/list/{labSn}")
    public List<B1711DTO> list(@PathVariable String labSn) {
        List<B1711> list = b1711Service.lambdaQuery().eq(B1711::getLabSn,labSn).list();
        return list.stream().map(b1711Converter::entityToDto).toList();
    }
} 