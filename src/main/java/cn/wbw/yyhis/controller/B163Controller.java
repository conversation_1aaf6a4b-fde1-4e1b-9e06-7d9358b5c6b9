package cn.wbw.yyhis.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import cn.wbw.yyhis.common.ApiResult;
import cn.wbw.yyhis.model.dto.B163DTO;
import cn.wbw.yyhis.model.entity.B163;
import cn.wbw.yyhis.service.B163Service;
import cn.wbw.yyhis.converter.B163Converter;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

import java.util.List;

/**
 * <p>
 * 分子病理检测记录 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-26
 */
@Tag(name = "分子病理检测记录")
@RestController
@RequestMapping("/b163")
public class B163Controller {

    @Autowired
    private B163Service b163Service;

    @Autowired
    private B163Converter b163Converter;

    @Operation(summary = "修改")
    @PutMapping("/update")
    public Boolean update(@RequestBody B163DTO b163DTO) {
        return b163Service.updateDto(b163DTO);
    }

    @Operation(summary = "删除")
    @DeleteMapping("/delete/{id}")
    public Boolean delete(@PathVariable("id") String id) {
        return b163Service.deleteByReportNo(id);
    }


    @Operation(summary = "查询所有分子病理检测记录")
    @GetMapping("/list/{visitSn}")
    public List<B163DTO> list(@PathVariable String visitSn) {
        List<B163> list = b163Service.lambdaQuery().eq(B163::getVisitSn, visitSn).list();
        return list.stream().map(b163Converter::entityToDto).toList();
    }
}