package cn.wbw.yyhis.controller;

import cn.wbw.yyhis.converter.B0616Converter;
import cn.wbw.yyhis.model.dto.B0616DTO;
import cn.wbw.yyhis.model.dto.B0616UpsertDTO;
import cn.wbw.yyhis.model.entity.B0616;
import cn.wbw.yyhis.service.B0616Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/b0616")
@RequiredArgsConstructor
@Tag(name = "B0616", description = "手术记录")
public class B0616Controller {

    private final B0616Service b0616Service;
    private final B0616Converter b0616Converter = B0616Converter.INSTANCE;

    @PostMapping("/save")
    public B0616DTO save(@RequestBody B0616UpsertDTO dto) {
        if (!StringUtils.hasText(dto.getRecordSn())) {
            B0616 newB0616 = b0616Service.addSurgeryRecord(dto);
            return b0616Converter.toDto(newB0616);
        } else {
            b0616Service.updateSurgeryRecord(dto);
            return b0616Converter.toDto(b0616Service.getById(dto.getRecordSn()));
        }
    }

    @DeleteMapping("/delete/{visitSn}")
    public Boolean delete(@PathVariable String visitSn) {
        return b0616Service.deleteByVisitSn(visitSn);
    }

    @GetMapping("/detail/{visitSn}")
    public B0616DTO detail(@PathVariable String visitSn) {
        return b0616Converter.toDto(b0616Service.lambdaQuery().eq(B0616::getVisitSn, visitSn).one());
    }

    @GetMapping("/all")
    public List<B0616DTO> findAll() {
        List<B0616> list = b0616Service.list();
        return list.stream().map(b0616Converter::toDto).toList();
    }

    @GetMapping("/page")
    public IPage<B0616DTO> findPage(@RequestParam(defaultValue = "1") Integer pageNum,
                                               @RequestParam(defaultValue = "10") Integer pageSize) {
        Page<B0616> page = new Page<>(pageNum, pageSize);
        Page<B0616> b0616Page = b0616Service.page(page);
        IPage<B0616DTO> dtoPage = b0616Page.convert(b0616Converter::toDto);
        return dtoPage;
    }
} 