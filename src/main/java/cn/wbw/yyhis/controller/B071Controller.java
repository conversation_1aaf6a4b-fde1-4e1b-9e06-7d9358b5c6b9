package cn.wbw.yyhis.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.wbw.yyhis.common.ApiResult;
import cn.wbw.yyhis.converter.B071Converter;
import cn.wbw.yyhis.model.dto.B071DTO;
import cn.wbw.yyhis.model.dto.B071UpsertDTO;
import cn.wbw.yyhis.model.entity.B023;
import cn.wbw.yyhis.model.entity.B071;
import cn.wbw.yyhis.service.B023Service;
import cn.wbw.yyhis.service.B071Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/b071")
@RequiredArgsConstructor
@Tag(name = "B071", description = "出院记录")
public class B071Controller {

    private final B071Service b071Service;
    private final B023Service b023Service;
    private final B071Converter b071Converter = B071Converter.INSTANCE;

    @PostMapping("/save")
    public ApiResult save(@RequestBody B071UpsertDTO dto) {
        B071DTO dto1;
        if (!StringUtils.hasText(dto.getRecordSn())) {
            return b071Service.addDischargeRecord(dto);
        } else {
            return b071Service.updateDischargeRecord(dto);
        }

    }

    @DeleteMapping("/delete/{visitSn}")
    public Boolean delete(@PathVariable String visitSn) {
        return b071Service.deleteByVisitSn(visitSn);
    }

    @GetMapping("/detail/{visitSn}")
    public B071DTO detail(@PathVariable String visitSn) {
        B071 one = b071Service.lambdaQuery().eq(B071::getVisitSn, visitSn).one();
        if (one != null) {
            // 一次查询获取所有诊断数据，按类型分组
            Map<String, List<B023>> diagnosisMap = b023Service.lambdaQuery()
                    .eq(B023::getVisitSn, visitSn)
                    .in(B023::getDiagType, "入院诊断", "出院诊断")
                    .select(B023::getDiagName, B023::getDiagType)
                    .list()
                    .stream()
                    .collect(Collectors.groupingBy(B023::getDiagType));

            // 设置入院诊断
            String admissionDiag = buildDiagnosisString(diagnosisMap.get("入院诊断"));
            one.setAdmissionDiag(admissionDiag);

            // 设置出院诊断
            String dischargeDiag = buildDiagnosisString(diagnosisMap.get("出院诊断"));
            one.setDischargeDiag(dischargeDiag);
        }
        return b071Converter.toDto(one);
    }

    @GetMapping("/all")
    public List<B071DTO> findAll() {
        List<B071> list = b071Service.list();
        return list.stream().map(b071Converter::toDto).toList();
    }

    @GetMapping("/page")
    public IPage<B071DTO> findPage(@RequestParam(defaultValue = "1") Integer pageNum,
                                   @RequestParam(defaultValue = "10") Integer pageSize) {
        Page<B071> page = new Page<>(pageNum, pageSize);
        Page<B071> b071Page = b071Service.page(page);
        IPage<B071DTO> dtoPage = b071Page.convert(b071Converter::toDto);
        return dtoPage;
    }

    /**
     * 构建诊断字符串
     *
     * @param diagnosisList 诊断列表
     * @return 诊断字符串，多个诊断用"，"分隔
     */
    private String buildDiagnosisString(List<B023> diagnosisList) {
        if (CollectionUtil.isEmpty(diagnosisList)) {
            return "";
        }
        return diagnosisList.stream()
                .map(B023::getDiagName)
                .collect(Collectors.joining("，"));
    }
}