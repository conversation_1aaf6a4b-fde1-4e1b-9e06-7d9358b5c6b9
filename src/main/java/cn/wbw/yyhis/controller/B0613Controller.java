package cn.wbw.yyhis.controller;

import cn.wbw.yyhis.converter.B0613Converter;
import cn.wbw.yyhis.model.dto.B0613DTO;
import cn.wbw.yyhis.model.dto.B0613UpsertDTO;
import cn.wbw.yyhis.model.entity.B0613;
import cn.wbw.yyhis.service.B0613Service;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 术前讨论 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@RestController
@RequestMapping("/b0613")
@RequiredArgsConstructor
public class B0613Controller {

    private final B0613Service b0613Service;
    private final B0613Converter converter = B0613Converter.INSTANCE;

    @PostMapping("/save")
    public B0613DTO save(@RequestBody B0613UpsertDTO dto) {
        if (!StringUtils.hasText(dto.getRecordSn())) {
            B0613 newB0613 = b0613Service.addPreoperativeDiscussion(dto);
            return converter.entityToDto(newB0613);
        } else {
            b0613Service.updatePreoperativeDiscussion(dto);
            return converter.entityToDto(b0613Service.getById(dto.getRecordSn()));
        }
    }

    @DeleteMapping("/delete/{visitSn}")
    public Boolean delete(@PathVariable String visitSn) {
        return b0613Service.deleteByVisitSn(visitSn);
    }

    @GetMapping("/all")
    public List<B0613DTO> findAll() {
        return converter.entityListToDtoList(b0613Service.list());
    }

    @GetMapping("/detail/{visitSn}")
    public B0613DTO detail(@PathVariable String visitSn) {
        return converter.entityToDto(b0613Service.lambdaQuery().eq(B0613::getVisitSn, visitSn).one());
    }

    @GetMapping("/page")
    public Page<B0613DTO> findPage(@RequestParam(defaultValue = "1") Integer pageNum,
                                   @RequestParam(defaultValue = "10") Integer pageSize) {
        Page<B0613> page = new Page<>(pageNum, pageSize);
        Page<B0613> entityPage = b0613Service.page(page);
        Page<B0613DTO> dtoPage = new Page<>(entityPage.getCurrent(), entityPage.getSize(), entityPage.getTotal());
        dtoPage.setRecords(converter.entityListToDtoList(entityPage.getRecords()));
        return dtoPage;
    }
} 