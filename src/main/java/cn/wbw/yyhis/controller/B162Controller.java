package cn.wbw.yyhis.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import cn.wbw.yyhis.common.ApiResult;
import cn.wbw.yyhis.model.dto.B162DTO;
import cn.wbw.yyhis.model.entity.B162;
import cn.wbw.yyhis.service.B162Service;
import cn.wbw.yyhis.converter.B162Converter;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

import java.util.List;

/**
 * <p>
 * 病理检查记录 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-26
 */
@Tag(name = "病理检查记录")
@RestController
@RequestMapping("/b162")
public class B162Controller {

    @Autowired
    private B162Service b162Service;

    @Autowired
    private B162Converter b162Converter;


    @Operation(summary = "修改")
    @PutMapping("/update")
    public Boolean update(@RequestBody B162DTO b162DTO) {
        return b162Service.updateDto(b162DTO);
    }

    @Operation(summary = "删除")
    @DeleteMapping("/delete/{id}")
    public Boolean delete(@PathVariable("id") String id) {
        return b162Service.deleteByReportNo(id);
    }


    @Operation(summary = "查询所有病理检查记录")
    @GetMapping("/list/{visitSn}")
    public List<B162DTO> list(@PathVariable String visitSn) {
        List<B162> list = b162Service.lambdaQuery().eq(B162::getVisitSn, visitSn).list();
        return list.stream().map(b162Converter::entityToDto).toList();
    }
}