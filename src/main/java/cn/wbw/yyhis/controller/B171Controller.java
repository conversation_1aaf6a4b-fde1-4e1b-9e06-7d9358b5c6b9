package cn.wbw.yyhis.controller;

import cn.wbw.yyhis.common.ApiResult;
import cn.wbw.yyhis.converter.B171Converter;
import cn.wbw.yyhis.model.dto.B171DTO;
import cn.wbw.yyhis.model.entity.B171;
import cn.wbw.yyhis.service.B171Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/b171")
@RequiredArgsConstructor
@Tag(name = "B171", description = "常规检验记录（主）")
public class B171Controller {

    private final B171Service b171Service;
    private final B171Converter b171Converter = B171Converter.INSTANCE;

    @Operation(summary = "查询所有常规检验记录（主）")
    @GetMapping("/list/{visitSn}")
    public List<B171DTO> list(@PathVariable String visitSn) {
        List<B171> list = b171Service.lambdaQuery().eq(B171::getVisitSn,visitSn).list();
        return list.stream().map(b171Converter::toDto).toList();
    }

} 