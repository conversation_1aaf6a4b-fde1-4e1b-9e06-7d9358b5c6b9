# YYHIS-Server 代码结构说明

## 项目结构

```
yyhis-server/
├── src/
│   ├── main/
│   │   ├── java/cn/wbw/yyhis/          # 主要源码包
│   │   └── resources/                   # 配置文件和资源
│   └── test/                           # 测试代码
├── sql/                                # SQL脚本
├── target/                             # 编译输出目录
├── pom.xml                             # Maven配置文件
└── mvnw, mvnw.cmd                      # Maven Wrapper
```

## 核心包结构

### 1. 主包结构 (`src/main/java/cn/wbw/yyhis/`)

```
cn.wbw.yyhis/
├── YyhisServerApplication.java         # Spring Boot 启动类
├── annotation/                         # 自定义注解
│   └── CompositeKey.java              # 复合主键注解
├── common/                            # 公共组件
│   ├── ApiResult.java                 # 统一API响应格式
│   ├── GlobalExceptionHandler.java    # 全局异常处理器
│   └── GlobalResponseAdvice.java      # 全局响应处理
├── config/                            # 配置类
│   ├── JacksonConfig.java             # JSON序列化配置
│   ├── MyBatisPlusConfig.java         # MyBatis Plus配置
│   └── SnakeCaseListSerializer.java   # 蛇形命名序列化器
├── constant/                          # 常量定义
│   └── IntegrationConstants.java      # 集成相关常量
├── controller/                        # 控制器层
├── converter/                         # 对象转换器(MapStruct)
├── exception/                         # 异常定义
│   └── BusinessException.java         # 业务异常
├── generator/                         # 代码生成器
├── mapper/                            # 数据访问层
├── model/                             # 数据模型
│   ├── dto/                          # 数据传输对象
│   ├── entity/                       # 实体类
│   ├── response/                     # 响应对象
│   └── vo/                           # 视图对象
├── service/                          # 服务层接口
│   └── impl/                         # 服务层实现
└── util/                             # 工具类
    ├── CompositeKeyUtil.java         # 复合主键工具
    ├── ExcelReader.java              # Excel读取工具
    ├── SchemaGenerator.java          # 数据库模式生成器
    ├── TnmDataImporter.java          # TNM数据导入工具
    └── model/                        # 工具类相关模型
```


### 2. 配置文件结构 (`src/main/resources/`)

```
resources/
├── application.yml                     # 主配置文件
├── application-dev.yml                 # 开发环境配置
├── application-prod.yml                # 生产环境配置
├── mapper/                            # MyBatis XML映射文件
│   └── B032Mapper.xml                 # 示例映射文件
└── templates/                         # 模板文件
    ├── ICD10编码.xlsx                 # ICD10编码模板
    ├── 临床TNM20250724.xlsx           # TNM分期模板
    ├── 字段目录.xlsx                  # 字段目录模板
    └── 医院实时数据集成文档/           # 集成文档
```
